{"name": "official-docs-pc", "description": "公文管理", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --mode dev", "build:uat": "vite build --mode uat", "build:uatrelease": "vite build --mode uatrelease", "build:main": "vite build --mode main", "preview": "vite preview", "prepare": "husky install"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@vueuse/core": "^10.4.1", "ant-design-vue": "^4.1.2", "axios": "^1.4.0", "dayjs": "^1.11.9", "echarts": "^5.6.0", "encryptlong": "^3.1.4", "isbn3": "^1.1.43", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "qs": "^6.11.2", "terser": "^5.21.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "3.3.12", "vue-color": "^3.2.0", "vue-router": "4"}, "devDependencies": {"@unocss/preset-rem-to-px": "^0.55.0", "@unocss/reset": "^0.55.0", "@vitejs/plugin-vue": "^4.2.3", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^14.0.1", "prettier": "3.0.3", "unocss": "0.60.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-html": "^3.2.0"}}