<template>
    <a-layout-sider :collapsed="collapsed" theme="light" width="208" :trigger="null" collapsible class="scrollbar sider_container">
        <div
            :class="[
                'logo',
                {
                    'flex-justify-center': collapsed,
                    'important-w-80': collapsed,
                },
            ]"
        >
       
            <img :src="config.logo" alt="logo.png" w-22 mr-8 />
            <span v-if="!collapsed">{{config.title}}</span>
        </div>
        <a-menu v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys" mode="inline" @click="handleClick" class="yd_root_menu">
            <template v-for="item in menuList" :key="item.name">
                <template v-if="!item.children">
                    <a-menu-item :key="item.name" v-if="!item.meta?.hiddenMenu">
                        <template #icon v-if="item.meta?.icon">
                            <i class="sider" :class="['iconfont', item.meta.icon]"></i>
                        </template>
                        {{ item.meta?.title }}
                    </a-menu-item>
                </template>
                <template v-else>                     
                    <SubMenu :menu-list="item" />
                </template>
            </template>
        </a-menu>
    </a-layout-sider>
</template>

<script setup>
import SubMenu from './SubMenu.vue'
import { useRoute, useRouter } from 'vue-router'
import routeList from '@/router/getRoute'
import config from "@/config"

defineProps({
    collapsed: {
        type: Boolean,
        default: false,
    },
})
const router = useRouter()
const route = useRoute()
const menuList = ref([])
const mainStore = useStore()
const init = async () => {    
    let arr = await routeList()
    menuList.value = arr[0]?.filter && arr[0]?.filter(i=> !['draftOperate','templateOperate','taohongOperate'].includes(i.name))
}
init()
const openKeys = ref([])
const selectedKeys = ref(route.matched.map(i => i.name))
watch(
    () => route.path,
    () => {
        selectedKeys.value = route.matched.map(i => i.name)
        openKeys.value = route.matched.map(i => i.name)
    },
    { immediate: true },
)

const handleClick = e => {
    router.push({ name: e.key })
}
</script>

<style lang="less" scoped>
.scrollbar {
    &::-webkit-scrollbar {
        width: 0px;
    }
}
.sider_container {
    box-shadow: 1px 0px 4px 0px #eceef1;
    // position: relative;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 500;

    :deep(.ant-menu-inline),
    :deep(.ant-menu-vertical) {
        border-inline-end: none;
    }
    .logo {
        position: absolute;
        top: 0;
        z-index: 9;
        background-color: #fff;
        width: 208px;
        height: 49px;
        display: flex;
        align-items: center;
        padding-left: 16px;
        color: #2c2c2c;
        font-weight: 600;
        img{
            width: auto;
            height: 22px;            
        }
    }
    .yd_root_menu {
        padding-top: 49px;
        :deep(.ant-menu-item) {
            &:hover {
                background-color: #e8f8f3;
                color: #19be8d;
            }
        }
        :deep(.ant-menu-submenu-title) {
            &:hover {
                background-color: #e8f8f3;
                color: #19be8d;
            }
        }
        :deep(.ant-menu-item-selected) {
            background-color: #e8f8f3;
            color: #19be8d;
            // @content: 'before';
            &:before {
                content: '';
                width: 4px;
                height: 26px;
                background-color: #19be8d;
                position: absolute;
                top: 5px;
                left: 0;
                border-radius: 0px 2px 2px 0px;
            }
            & > .ant-menu-submenu-title {
                color: #19be8d;
            }
            &:active {
                background-color: #e8f8f3;
            }
            &:hover {
                background-color: #e8f8f3;
            }
        }
        :deep(.ant-menu-submenu-selected) {
            & > .ant-menu-submenu-title {
                color: #19be8d;
            }
        }
        :deep(.ant-menu-item-active) {
            background-color: #e8f8f3;
            color: #19be8d;
        }
    }
}
</style>
