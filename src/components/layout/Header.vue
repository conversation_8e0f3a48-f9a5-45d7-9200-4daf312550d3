<template>
    <a-layout-header class="layout_header" :style="{ paddingLeft: definedProps.collapsed ? '80px' : '208px' }">
        <div class="header_left">
            <slot name="headerLeft"></slot>
            <!-- <a-button
                type="primary"
                class="bg-#00B781 mr-38 important-hover-bg-#00B781"
                shape="round"
                size="default"
                @click="router.push({name: 'home'})"
            >
                返回首页
            </a-button> -->
        </div>
        <div class="header_right">
            <a-dropdown arrow>
                <span class="header_right_item">
                    <a-avatar :size="26" v-if="mainStore.userInfo?.avatar" :src="mainStore.userInfo.avatar"></a-avatar>
                    <a-avatar :size="26" v-else src="/avatar.png"></a-avatar>
                    <span pl-4>{{ mainStore.userInfo?.username || mainStore.userInfo.name}}</span>
                </span>
                <template #overlay>
                    <a-menu>
                        <a-menu-item @click="signOut">
                            <template #icon>
                                <i class="iconfont icon-icon-tcxt1"></i>
                            </template>
                            退出登录
                        </a-menu-item>
                    </a-menu>
                </template>
            </a-dropdown>
        </div>
    </a-layout-header>
</template>

<script setup>
const router = useRouter()
const mainStore = useStore()

const definedProps = defineProps({
    collapsed: {
        type: Boolean,
        default: false,
    },
})

const signOut = async () => {
    try {       
        window.localStorage.clear()
        window?.close()
        router.push({
            path:"/login"
        })
    } catch (error) {
        YMessage.error(error.message)
    }
}
</script>

<style lang="less" scoped>
.layout_header {
    background: #fff;
    padding: 0;
    height: 48px;
    line-height: 48px;
    display: flex;
    justify-content: space-between;

    position: fixed;
    top: 0; 
    left: 0;
    right: 0;
    z-index: 409;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
    padding-left: 208px;

    .header_left {
        display: flex;
        align-items: center;
        .btn_item {
            display: inline-block;
            margin-left: 48px;
            cursor: pointer;
            &:hover {
                color: #00b781;
            }
            &:first-of-type {
                margin-left: 0px;
            }
        }
    }
    .header_right {
        margin-right: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .header_right_l {
            display: flex;
            align-items: center;
            & > span {
                color: #2c2c2c;
                padding-right: 16px;
                margin-right: 16px;
                font-size: 14px;
                cursor: pointer;
                line-height: 18px;
                border-right: 1px solid #999999;
                &:hover {
                    color: #00b781;
                }
            }
        }

        .header_right_item {
            margin: 0 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        .header_right_item:hover {
            color: #19be8d;
        }
        .iconfont {
            font-size: 18px;
        }

        .dormitory-box {
            display: flex;
            align-items: center;
            height: 30px;
            box-sizing: border-box;
            background: #ebfaf5;
            border: 1px solid #00b781;
            border-radius: 30px;
            padding: 0 14px;
            span {
                font-size: 14px;
                font-weight: 400;
                color: #00b781;
            }
        }
    }
    .cloud_platform_btn {
        border-radius: 32px;
        color: rgb(0, 183, 129);
    }
}
</style>
