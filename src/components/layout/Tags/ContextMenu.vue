<template>
    <a-menu>
        <a-menu-item v-for="item in options" :key="item.key" @click="handleSelect(item.key)">
            {{ item.label }}
        </a-menu-item>
    </a-menu>
</template>

<script setup>
const props = defineProps({
    ctag: {
        type: Object,
        default: {},
    },
})
const route = useRoute()
const router = useRouter()
const store = useStore()
const options = computed(() => {
    if (props.ctag.key != 'home') {
        return [
            {
                label: '重新加载',
                key: 'reload',
            },
            {
                label: '关闭',
                key: 'close',
            },
            {
                label: '关闭其他',
                key: 'close-other',
            },
            {
                label: '关闭左侧',
                key: 'close-left',
            },
            {
                label: '关闭右侧',
                key: 'close-right',
            },
        ]
    } else {
        return [
            {
                label: '重新加载',
                key: 'reload',
            },
            {
                label: '关闭其他',
                key: 'close-other',
            },
            {
                label: '关闭右侧',
                key: 'close-right',
            },
        ]
    }
})
const close = async () => {
    if (props.ctag.key == route.name) {
        let index = store.tags.findIndex(i => i.key == props.ctag.key)
        let item = store.tags[index + 1] || store.tags[index - 1]
        router.push({ name: item.key })
        store.tags.splice(index, 1)
    } else {
        let index = store.tags.findIndex(i => i.key == props.ctag.key)
        store.tags.splice(index, 1)
    }
}
const closeOther = () => {
    let item = store.tags.find(i => i.key == props.ctag.key)
    if (item.key == 'check') {
        store.tags = [item]
    } else {
        store.tags = [
            {
                key: 'check',
                title: '图书查重',
            },
            item,
        ]
    }
    router.push({ name: props.ctag.key })
}
const closeDirection = direction => {
    let index = store.tags.findIndex(i => i.key == props.ctag.key)
    if (direction == 'left') {
        let arr = store.tags.slice(index, store.tags.length)
        store.tags = [store.tags[0], ...arr]
    } else {
        store.tags = store.tags.slice(0, index + 1)
    }
    router.push({ name: props.ctag.key })
}
const reload = () => {
    store.exTag = route.name
    router.push({ path: '/redirect', query: { path: route.path } })
    setTimeout(() => {
        store.exTag = ''
    }, 0)
}
const actionMap = new Map([
    ['reload', reload],
    ['close', close],
    ['close-other', closeOther],
    [
        'close-left',
        () => {
            closeDirection('left')
        },
    ],
    [
        'close-right',
        () => {
            closeDirection('right')
        },
    ],
])

function handleSelect(key) {
    const actionFn = actionMap.get(key)
    actionFn && actionFn()
}
</script>
