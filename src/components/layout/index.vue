<template>
    <a-layout class="layou_box">
        <sider :collapsed="mainStore.collapsed"></sider>
        <a-layout class="right_container" :style="{ marginLeft: mainStore.collapsed ? '80px' : '208px' }">
            <Header :collapsed="mainStore.collapsed">
                <template #headerLeft>
                    <svg-icon
                        name="icon-icon_open"
                        v-if="mainStore.collapsed"
                        @click="() => (mainStore.collapsed = !mainStore.collapsed)"
                    ></svg-icon>
                    <svg-icon name="icon-icon_up" v-else @click="() => (mainStore.collapsed = !mainStore.collapsed)"></svg-icon>
                </template>
            </Header>
            <div class="tags_container">
                <Tags></Tags>
            </div>

            <Content></Content>
        </a-layout>
    </a-layout>
</template>
<script setup>
const mainStore = useStore()
</script>

<style lang="less" scoped>
.layou_box {
    height: 100vh;     
    svg {
        margin-left: 24px;
        margin-right: 16px;
        cursor: pointer;
    }
    .right_container {
        background-color: #f7f8fa;
        margin-left: 208px;
    }

    .tags_container{
        margin-top: 48px;
    }
}


</style>
