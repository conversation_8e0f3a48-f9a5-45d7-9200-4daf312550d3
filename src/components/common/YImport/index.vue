<template>
    <div>
        <a-modal
            :open="open"
            :title="title"
            :destroyOnClose="true"
            @ok="handleOk"
            :okText="okText"
            @cancel="cancel"
            :confirmLoading="confirmLoading"
            width="700px"
        >
            <div p-20>
                <div v-if="state.page === PAGES_STATUS.UPLOAD">
                    <slot name="tips"></slot>
                    <!-- 存在自定义提示，隐藏默认提示 -->
                    <ul class="step">
                        <li>
                            <p>一、下载导入模板，批量填写数据信息</p>
                            <a-button class="down-btn">
                                <a class="down-link" :href="templateSrc">
                                    <img src="@/assets/images/download.png" />
                                    <span>下载模版</span>
                                </a>
                            </a-button>
                        </li>
                        <li>
                            <p>二、上传填写好的信息表</p>
                            <!-- 额外配置 -->
                            <slot name="query"></slot>
                            <a-upload
                                :disabled="disabled"
                                :showUploadList="false"
                                :multiple="false"
                                :customRequest="customRequest"
                                :accept="accept"
                            >
                                <a-button class="btn" ref="uploadRef">
                                    <img src="@/assets/images/upload.png" />
                                    <span>上传文件</span>
                                </a-button>
                            </a-upload>
                        </li>
                    </ul>

                    <div class="fileList">
                        <ul>
                            <li v-for="(item, index) in fileList" :key="index">
                                <div class="li-item" v-if="item.status == 0">
                                    <div class="file-name">
                                        <img class="link" src="@/assets/images/link.png" alt="link.png" />
                                        <p>{{ item.name }}</p>
                                        <img
                                            class="close"
                                            src="@/assets/images/close.png"
                                            alt="close.png"
                                            @click="fileList.splice(index, 1)"
                                        />
                                    </div>
                                    <a-progress :percent="0" :showInfo="false" :size="2" />
                                    <span class="file-wait-upload">等待导入</span>
                                </div>
                                <div class="li-item" v-if="item.status == 1">
                                    <div class="file-name">
                                        <img class="link" src="@/assets/images/link.png" alt="link.png" />
                                        <p>{{ item.name }}</p>
                                    </div>
                                    <a-progress :percent="item.schedule" :showInfo="false" strokeColor="#00B781" :size="2" />
                                    <span class="file-uploading">正在上传中…</span>
                                </div>
                                <div class="li-item" v-if="item.status == 2">
                                    <div class="file-name">
                                        <img class="link" src="@/assets/images/link.png" alt="link.png" />
                                        <p>{{ item.name }}</p>
                                        <CheckCircleFilled class="check-icon" />
                                    </div>
                                    <a-progress :percent="100" :showInfo="false" strokeColor="#00B781" :size="2" />
                                    <span class="file-success">上传成功</span>
                                </div>
                                <div class="li-item" v-if="item.status == 3">
                                    <div class="file-name">
                                        <img class="link" src="@/assets/images/link.png" alt="link.png" />
                                        <p>{{ item.name }}</p>
                                        <img class="reset" src="@/assets/images/reset.png" alt="reset.png" @click="resetUpload(index)" />
                                        <img
                                            class="close-error"
                                            src="@/assets/images/close.png"
                                            alt="close.png"
                                            @click="fileList.splice(index, 1)"
                                        />
                                    </div>
                                    <a-progress :percent="100" :showInfo="false" strokeColor="#F5222D" :size="2" />
                                    <span class="file-error">上传失败</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div v-else class="upload-result">
                    <div class="upload-info">
                        <div class="upload-info-num" v-if="isShowRepeat">
                            <div v-if="state.result.itemsSucceeded">
                                <!-- 正确：正常、重复 -->
                                <p>
                                    正常数据条数：
                                    <span class="color-#00B781">{{ state.result.itemsSucceeded || 0 }}</span>
                                    条
                                </p>
                                <p>
                                    重复数据条数：
                                    <span class="color-#FCC68E">{{ repeatNum }}</span>
                                    条
                                </p>
                            </div>
                            <div v-else>
                                <!-- 错误：正常、异常 -->
                                <p>
                                    正常数据条数：
                                    <span class="color-#00B781">{{ repeatNum }}</span>
                                    条
                                </p>
                                <p>
                                    异常数据条数：
                                    <span class="color-#FE6565">{{ state.result.itemsFailed || 0 }}</span>
                                    条
                                </p>
                            </div>
                        </div>
                        <div v-else class="upload-info-num">
                            <p>
                                正常数据条数：
                                <span class="color-#00B781">{{ state.result.itemsSucceeded || 0 }}</span>
                                条
                            </p>
                            <p>
                                异常数据条数：
                                <span class="color-#FE6565">{{ state.result.itemsFailed || 0 }}</span>
                                条
                            </p>
                        </div>
                        <a-button class="btn" @click="exportSuccess" :disabled="disableDownload" v-if="isDownSuccess">下载文件</a-button>
                        <a-button @click="exportError" class="btn" :disabled="!state.result.errorUrl" v-else>下载异常数据</a-button>
                    </div>
                    <a-button
                        type="link"
                        class="btn-link-color download-error"
                        v-if="isDownSuccess"
                        :disabled="!state.result.errorUrl"
                        @click="exportError"
                    >
                        <i class="iconfont icon-icon-xiazai"></i>
                        下载异常数据
                    </a-button>
                </div>
            </div>
        </a-modal>
    </div>
</template>
<script setup>
import { createVNode } from 'vue'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    templateSrc: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: '导入',
    },
    uploadRequest: {
        type: Function,
        required: true,
    },
    progressRequest: {
        type: Function,
        required: true,
    },
    errorExport: {
        type: Function,
        required: false,
    },
    // 再confirm之前校验是否继续
    beforeConfirm: {
        type: Function,
        required: false,
    },
    isDownSuccess: {
        type: Boolean,
        default: false,
    },
    isShowRepeat: {
        type: Boolean,
        default: false,
    },
    accept: {
        type: String,
        default: '.xlsx,.xls',
    },
})

// *********************
// Hooks Function
// *********************
const emit = defineEmits(['update:show', 'reUpload'])

const open = ref(false)
const confirmLoading = ref(false)
const fileList = ref([])
const okText = ref('导入')
const downloadLoading = ref(false)
const disabled = ref(false)
const importIdList = ref([])
const uploadRef = ref(null)

const PAGES_STATUS = {
    UPLOAD: 1,
    RESULT: 2,
}

const state = reactive({
    page: PAGES_STATUS.UPLOAD,
    result: {},
})

const disableDownload = computed(() => {
    return state.result.status !== 'succeeded' || !state.result.succeededUrl
})

const repeatNum = computed(() => {
    // 重复数: 总数-成功-失败
    const { itemsTotal = 0, itemsSucceeded = 0, itemsFailed = 0 } = state.result
    return itemsTotal - itemsSucceeded - itemsFailed
})

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const cancel = () => {
    const isUploading = fileList.value.some(item => item.status === 1)
    if (isUploading) {
        Modal.confirm({
            title: `提示`,
            icon: createVNode(ExclamationCircleFilled),
            content: `文件上传中， 关闭弹窗将无法及时查看结果`,
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                emit('update:show', false)
                emit('cancel')
            },
            onCancel() {},
        })
    } else {
        emit('update:show', false)
        emit('cancel')
    }
}

const exportSuccess = () => {
    downloadFile(state.result.succeededUrl)
}

const exportError = () => {
    downloadFile(state.result.errorUrl)
}
const handleOk = async () => {
    const isContinue = props.beforeConfirm ? await props.beforeConfirm() : true

    if (!isContinue) return

    if (okText.value == '继续导入') {
        state.page = PAGES_STATUS.UPLOAD
        okText.value = '导入'
        disabled.value = false
        fileList.value = []
        importIdList.value = []
        state.result = {}
        emit('reUpload')
        return false
    }
    if (disabled.value) {
        state.page = PAGES_STATUS.RESULT
        okText.value = '继续导入'
        return false
    }
    importIdList.value = []
    state.result = {}
    if (fileList.value.length == 0) {
        YMessage.error('请先上传文件')
        return
    }
    confirmLoading.value = true
    disabled.value = true
    for (let i = 0; i < fileList.value.length; i++) {
        const item = fileList.value[i]
        await importItem(item)
    }
    confirmLoading.value = false
    state.page = PAGES_STATUS.RESULT
    okText.value = '继续导入'
}
const importItem = item => {
    return new Promise(async (resolve, reject) => {
        try {
            const { data } = await props.uploadRequest({ file: item.file })
            importIdList.value.push(data)
            item.status = 1
            item.schedule = 0
            const timer = setInterval(async () => {
                const progress = await props.progressRequest(data)
                // 每次轮询+10，模拟进度条
                item.schedule = item.schedule >= 89 ? item.schedule : item.schedule + 10

                state.result = progress
                if (progress.status === 'succeeded') {
                    item.status = 2
                    clearInterval(timer)
                    resolve()
                } else if (progress.status === 'failed') {
                    item.status = 3
                    clearInterval(timer)
                    resolve()
                }
                if (!open.value) {
                    item.status = 3
                    clearInterval(timer)
                    resolve()
                }
            }, 1000)
        } catch (error) {
            item.status = 3
            resolve()
        }
    })
}

const customRequest = e => {
    const size = (e.file.size / 1024 / 1024).toFixed(2)
    fileList.value.push({ file: e.file, status: 0, name: e.file.name, size })
}

const resetUpload = index => {
    // 去掉这条数据，重新上传
    fileList.value.splice(index, 1)
    uploadRef.value.$el.click()
}

// *********************
// Watch Function
// *********************

watch(
    () => props.show,
    val => {
        state.page = PAGES_STATUS.UPLOAD
        fileList.value = []
        open.value = props.show
        disabled.value = false
        okText.value = '导入'
        downloadLoading.value = false
    },
)
</script>
<style lang="less" scoped>
.import-tips {
    background: #f8f8fa;
    border-radius: 4px;
    padding: 16px 20px;
}

.step {
    p {
        font-weight: 600;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        margin-top: 24px;
    }
    .btn,
    .down-btn {
        display: flex;
        align-items: center;
        background: rgba(0, 183, 129, 0.08);
        border-radius: 4px;
        border: 1px solid #00b781;
        font-weight: 400;
        font-size: 14px;
        color: #00b781;
        margin-top: 16px;
        img {
            margin-right: 4px;
        }
    }

    .down-btn {
        padding: 0;
    }

    .down-link {
        padding: 4px 15px;
    }
}

.upload-result {
    .upload-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f8fa;
        border-radius: 4px;
        padding: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #2c2c2c;
    }

    .btn {
        font-weight: 400;
        font-size: 14px;
        color: #00b781;
        border-radius: 4px;
        border: 1px solid #00b781;
        &:disabled {
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
        }
    }
}
.fileList {
    .li-item {
        margin-top: 16px;
    }

    .file-name {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        .link {
            width: 14px;
            height: 14px;
            margin-right: 8px;
        }
        .reset {
            margin-left: auto;
            cursor: pointer;
        }
        .close-error {
            margin-left: 12px;
            cursor: pointer;
        }
        .close {
            margin-left: auto;
            cursor: pointer;
        }
    }

    :deep(.ant-progress) {
        margin: 0;
    }

    .file-wait-upload,
    .file-uploading {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }

    .check-icon {
        color: #00b781;
        margin-left: auto;
    }

    .file-icon {
        position: absolute;
        top: 24px;
        right: 24px;
        font-size: 16px;
    }
    .file-error {
        font-weight: 400;
        font-size: 14px;
        color: #f5222d;
    }
    .file-success {
        font-weight: 400;
        font-size: 14px;
        color: #00b781;
    }
}

.download-error {
    display: block;
    margin-left: auto;
    margin-top: 16px;
}
</style>
