<template>
    <div text-right v-if="show" mt-20>
        <a-pagination
            class="yd_pagination_box"
            :pageSizeOptions="pageSizeOptions"
            show-quick-jumper
            show-less-items
            showSizeChanger
            :total="props.total"
            :current="props.current"
            @change="paginationChange"
            :show-total="total => `共 ${total} 条`"
        />
    </div>
</template>
<script setup>
import { watch } from 'vue'
const show = ref(false)
const props = defineProps({
    total: {
        type: Number,
        default: 0,
    },
    current: {
        type: Number,
        default: 1,
    },
})
watch(
    () => props.total,
    total => {
        show.value = total > 10 ? true : false
    },
    {
        immediate: true,
    },
)
const pageSizeOptions = ['10', '20', '30', '40', '50', '100']
const emit = defineEmits(['paginationChange'])
const paginationChange = (pageNo, pageSize) => {
    emit('paginationChange', { pageNo, pageSize })
}
</script>
<style lang="less" scoped>
.yd_pagination_box {
    :deep(.ant-pagination-item) {
        border: 1px solid #d9d9d9;
    }
    :deep(.ant-pagination-item-active) {
        background-color: @primary-color;
        a {
            color: #fff;
        }
    }
}
</style>
