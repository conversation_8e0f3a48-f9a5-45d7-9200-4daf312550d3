<template>
    <a-modal
        class="mSelect"
        v-model:visible="modelState.open"
        :destroyOnClose="true"
        :maskClosable="false"
        title="选择"
        width="800px"
        @cancel="onCancel"
        @ok="onOk"
        :bodyStyle="{ padding: 0 }"
        cancelText="取消"
        okText="确定"
    >
        <div class="mSelect-wrap" v-if="modelState.open">
            <div class="section" :class="{ active: !isShowSearch }">
                <!--  分类    -->
                <div class="tabs">
                    <a-radio-group
                        v-model:value="activeTabIndex"
                        button-style="solid"
                        @change="onTabsChange"
                    >
                        <a-radio-button
                            v-for="(item, index) in props.tabs"
                            :key="item.id"
                            :value="index"
                        >
                            {{ item.tab }}
                        </a-radio-button>
                    </a-radio-group>
                </div>
                <!-- 搜索 -->
                <div
                    class="reset-input-search"
                    :class="{ active: isShowSearch }"
                >
                    <a-input-search
                        v-if="isShowSearch"
                        allowClear
                        v-model:value.trim="state.inputGroup.name"
                        placeholder="请输入搜索的内容"
                        @search="handleSearch"
                    >
                        <template
                            v-if="tabs[activeTabIndex].isClassify"
                            #addonBefore
                        >
                            <a-select
                                v-model:value.trim="
                                    state.inputGroup.accommodation
                                "
                                style="width: 80px"
                            >
                                <a-select-option value="">全部</a-select-option>
                                <a-select-option
                                    v-for="it in accommodationOpt"
                                    :key="it.value"
                                    :value="it.value"
                                >
                                    {{ it.label }}
                                </a-select-option>
                            </a-select>
                        </template>
                    </a-input-search>
                </div>
                <!-- 搜索 -->
                <!-- <a-input-search
                    class="reset-input-search"
                    v-if="isShowSearch"
                    v-model:value.trim="state.name"
                    placeholder="请输入搜索的内容"
                    @search="handleSearch"
                    allow-clear
                /> -->
                <div p-20 v-if="state.isSearchTable">
                    <SearchTable
                        :data="modelState.searchTable"
                        :loading="modelState.loading"
                        :single="activeTab.single || !!maxOptional"
                        :selectKeys="state.selectedRowKeys"
                        @change="onSelectChange"
                        @paginationChange="searchByPage"
                    />
                </div>
                <div v-else class="select-wrap">
                    <!-- 面包屑 -->
                    <a-breadcrumb>
                        <template #separator>
                            <a-avatar
                                shape="square"
                                :src="arrow"
                                :size="20"
                            ></a-avatar>
                        </template>

                        <template
                            v-for="(item, index) in state.breadcrumbs"
                            :key="item[fieldNames.value]"
                        >
                            <a-breadcrumb-item
                                href=""
                                @click="handleBreadcrumb(item, index)"
                            >
                                {{ item.showName || item[fieldNames.label] }}
                            </a-breadcrumb-item>
                        </template>
                    </a-breadcrumb>
                    <div class="spinning" v-if="modelState.loading">
                        <a-spin :spinning="modelState.loading" />
                    </div>
                    <!-- 源数据 -->
                    <div class="structures" @scroll="handleScroll($event)">
                        <template v-if="modelState._dataSource?.length">
                            <div class="row" mt-16 v-if="isShowAllSelect">
                                <a-checkbox
                                    :disabled="
                                        modelState.isaAuthority || maxOptional
                                    "
                                    class="check checkbox"
                                    :checked="checkAll"
                                    @change="onCheckAllChange"
                                >
                                    <span style="padding-top: 8px">全选</span>
                                </a-checkbox>
                            </div>

                            <component
                                :value="originCheckedList"
                                style="width: 100%"
                                :is="
                                    activeTab.single
                                        ? ARadio.Group
                                        : ACheckbox.Group
                                "
                                :key="Date.now()"
                            >
                                <div
                                    class="row tree"
                                    v-for="(item, i) in modelState._dataSource"
                                    :key="item.id"
                                >
                                    <div class="tree-item">
                                        <component
                                            class="check"
                                            :class="{
                                                'check-visible':
                                                    !isCheckVisible(item),
                                            }"
                                            :is="
                                                activeTab.single
                                                    ? ARadio
                                                    : ACheckbox
                                            "
                                            :value="itemValue(item)"
                                            :disabled="
                                                item.disabled ||
                                                modelState.isaAuthority ||
                                                (maxOptional &&
                                                    state.selectedList.length ==
                                                        maxOptional)
                                            "
                                            name="check"
                                            :key="item.id"
                                            @change="
                                                onCheckChange($event, item)
                                            "
                                        >
                                            <a-avatar
                                                shape="square"
                                                :src="
                                                    (!isPerson(item) &&
                                                        structure) ||
                                                    ''
                                                "
                                                :size="36"
                                            >
                                                <span v-if="isPerson(item)">
                                                    {{
                                                        item.studentName?.slice(
                                                            -2
                                                        ) ||
                                                        item.name?.slice(-2)
                                                    }}
                                                </span>
                                            </a-avatar>

                                            <div class="cnt">
                                                <div
                                                    class="label ellipsis"
                                                    :title="
                                                        item[fieldNames.label]
                                                    "
                                                >
                                                    {{
                                                        item.showName ||
                                                        item[fieldNames.label]
                                                    }}
                                                </div>
                                            </div>
                                        </component>
                                        <div
                                            class="tree-item-eltern"
                                            v-if="item.elterns?.length"
                                        >
                                            <div
                                                class="eltern-item"
                                                v-for="it in item.elterns"
                                                :key="it.id"
                                            >
                                                <a-avatar
                                                    shape="square"
                                                    :src="
                                                        (!isPerson(it) &&
                                                            structure) ||
                                                        ''
                                                    "
                                                    :size="36"
                                                >
                                                    <span v-if="isPerson(it)">
                                                        {{ it.name?.slice(-2) }}
                                                    </span>
                                                </a-avatar>
                                                <div class="cnt">
                                                    {{ it.name }}
                                                    <span class="identity">
                                                        {{
                                                            relations(
                                                                it.relations
                                                            )
                                                        }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="more" v-if="isShowMore(item)">
                                        <span
                                            class="more-next acitve"
                                            v-if="
                                                type !== 'people' &&
                                                prohibitNextStep(item.id)
                                            "
                                        >
                                            下级
                                        </span>
                                        <span
                                            class="more-next"
                                            v-else
                                            @click="handleMore($event, item)"
                                            >下级</span
                                        >
                                    </div>
                                </div>
                            </component>
                        </template>
                        <div v-else class="data-empty">
                            <img src="/image/venues.png" />
                            <p>暂时没有找到相关结果</p>
                        </div>
                    </div>
                </div>
            </div>
            <SelectedArea
                :list="state.selectedList"
                :type="type"
                :tabs="tabs"
                :fieldNames="fieldNames"
                @clear="clearAllSelected"
                @deleteItem="deleteSelectedItem"
            />
        </div>
    </a-modal>
</template>

<script setup>
import structure from "/image/icon-structure.png";
import arrow from "/image/icon-arrow.png";
import { Checkbox as ACheckbox, Radio as ARadio } from "ant-design-vue";
import { SELECT_TYPE, DISPLAY_MODE, TRIGGER_MAP } from "./constants";
import SelectedArea from "./SelectedArea.vue";
import SearchTable from "./SearchTable.vue";
import { useStore } from "@/store/index";

// *********************
// Hooks Function
// *********************
const store = useStore();

const props = defineProps({
    // 用法看上面
    tabs: {
        type: Array,
        default: () => [],
    },
    // 选择组件类型
    type: {
        type: String,
        default: SELECT_TYPE.DEPARTMENT,
    },
    // 已选择
    selected: {
        type: Array,
        default: () => [],
    },
    fieldNames: {
        type: Object,
        default: () => {
            return { label: "name", value: "id" };
        },
    },
    // 最多可选的数量
    maxOptional: {
        type: Number,
        default: 0,
    },
    // 是否可点面包屑
    canClickBreadcrumbs: {
        type: Boolean,
        default: false, // false:  可点击; true:不可点击
    },
});

const emit = defineEmits([
    "search",
    "toggleTabs",
    "toggleLevel",
    "onScroll",
    "cancel",
    "submit",
]);

// 就读方式的字典列表
const accommodationOpt = computed(() => {
    const { accommodation = [] } = store.state.selectSource.dictionary;
    return accommodation;
});
// $ 关联弹窗 （提供外部修改的数据）
const modelState = reactive({
    open: false, // 显示弹框
    dataSource: [], // 左侧数据源
    _dataSource: [], // 左侧数据源
    loading: false, // loading
    searchTable: {
        // 选人搜索 table 中显示
        list: [],
        pageNo: 1,
        pageSize: 500,
        total: 0,
    },
    isPpresent: false, // 用于滚动条加载
});
const state = reactive({
    allSelect: true,
    isSearchTable: false,
    name: "",
    // 面包屑
    breadcrumbs: [
        {
            name: props.tabs[0]?.tab,
            id: 0,
            children: [],
        },
    ],
    selectedList: props.selected || [],
    selectedRowKeys: [],
    inputGroup: {
        name: "",
        accommodation: "",
    },
});

// *********************
// Service Function
// *********************

// 选择的表格数据
const onSelectChange = (ron, node) => {
    // maxOptional 如果有现在的数量大于maxOptional 就不允许选择
    if (props.maxOptional && ron.length > props.maxOptional) {
        return false;
    }
    const { id: _type, single } = activeTab.value;
    if (single) {
        // 需要区分_type，不能影响别的tab栏的数据
        const ids = modelState.searchTable.list?.map((item) => item.id) || [];
        // 找到当前table下的id
        const id = state.selectedRowKeys.find((id) => ids.includes(id));
        const reservedList = state.selectedList.filter(
            (item) => item._type !== _type
        );
        const reservedKeys = reservedList.map((item) => item.id);
        const selectNode = node
            .filter((row) => row.id !== id)
            .map((row) => ({ ...row, _type }));
        state.selectedRowKeys = selectNode.length
            ? [...reservedKeys, selectNode[0].id]
            : reservedKeys;
        state.selectedList = [...reservedList, ...selectNode];
    } else {
        // 只针对当前的table数据进行选择,保留不在这个table里的选中数据
        const ids = modelState.searchTable.list?.map((item) => item.id) || [];
        const reservedList = state.selectedList.filter(
            (item) => !ids.includes(item.id)
        );
        const reservedKeys = reservedList.map((item) => item.id);
        state.selectedRowKeys = [...reservedKeys, ...ron];
        const selectNode = node.map((v) => ({ ...v, _type }));
        state.selectedList = [...reservedList, ...selectNode];
    }
};

const itemValue = computed(() => {
    return (item) => item[props.fieldNames.value] || "";
});

// 当前选人业务
const activeTabIndex = ref(props.tabs.findIndex((item) => item.checked) || 0);
const activeTab = computed(() => {
    return props.tabs[activeTabIndex.value];
});

// 判断是人还是其他 （有一个tab符合就显示人。 后续可增加条件拓展）
const isPerson = computed(() => {
    return (item) => {
        if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.ALL].includes(props.type)) {
            // $ tab有一个符合就算是人（切换tab,保持头像必须不变）
            const isPerson = props.tabs.some((tab) => {
                // 或者直接判断对象是否存在改字段（例如：userId  人员特有的）
                // item.hasOwnProperty(tab.personField?.key)
                return (
                    tab.personField?.value.includes(
                        item[tab.personField?.key]
                    ) || item.hasOwnProperty(tab.personField?.key)
                );
            });
            return isPerson;
        }
        return false;
    };
});

const isCheckVisible = computed(() => {
    return (item) => {
        item = toRaw(item);
        if (props.type === SELECT_TYPE.DEPARTMENT) {
            // 选部门
            return true;
        } else if (
            activeTab.value.personField?.value.includes(item.rollValue)
        ) {
            // 自定义显示勾选框
            return true;
        } else if (props.type === SELECT_TYPE.PEOPLE) {
            // 选人
            const value = activeTab.value.personField?.value || [];
            // 或者直接判断对象是否存在改字段（例如：userId  人员特有的）
            let isPeople =
                value.includes(item[activeTab.value.personField?.key]) ||
                item.hasOwnProperty(activeTab.value.personField?.key) ||
                false;
            return isPeople;
        } else if (props.type === SELECT_TYPE.CLASS) {
            // 班级、宿舍
            const value = activeTab.value.personField?.value || [];
            const isClass = value.includes(
                item[activeTab.value.personField?.key]
            );
            return isClass;
        } else if (props.type === SELECT_TYPE.ALL) {
            // 选人和选部门
            return true;
        }
    };
});
const isShowAllSelect = computed(() => {
    //  activeTab.single 多选： false ， 单选：
    if (props.type == "people" && modelState.dataSource.length) {
        const item = modelState.dataSource[modelState.dataSource.length - 1];
        state.allSelect =
            item.hasOwnProperty(activeTab.value.personField?.key) ||
            item.hasOwnProperty("userId");
    } else if (props.type === SELECT_TYPE.CLASS) {
        // 只选班级
        const item = modelState.dataSource[modelState.dataSource.length - 1];
        state.allSelect = item.rollValue === "classes";
    } else if (props.type === SELECT_TYPE.ALL) {
        state.allSelect = true;
    }
    let disabledNum = 0;
    if (modelState.dataSource.length) {
        modelState.dataSource.forEach((it) => {
            if (it.disabled) disabledNum++;
        });
        return !(modelState.dataSource.length == disabledNum);
    }
    return !activeTab.value.single && state.allSelect;
});

const searchByPage = (pageNo, pageSize) => {
    const { name, accommodation } = state.inputGroup;
    const options = {
        name,
        accommodation,
        pageNo,
        pageSize,
    };
    const tabId = activeTab.value.id;
    emit("search", tabId, options);
};

// 搜索
const handleSearch = () => {
    // $ 默认使用新的搜索展示框，如果指定旧的就用旧的
    const { searchOption = { displayMode: DISPLAY_MODE.NEW } } =
        activeTab.value;
    const isOld = searchOption.displayMode === DISPLAY_MODE.OLD;
    state.isSearchTable =
        !isOld && (!!state.inputGroup.name || !!state.inputGroup.accommodation);
    // 旧的展示方式
    if (searchOption.displayMode === DISPLAY_MODE.OLD) {
        // 删除面包屑
        state.breadcrumbs.splice(1);
    }
    // 需要把选中的数据同步给table的select
    state.selectedRowKeys = state.selectedList.map((item) => item.id);
    // $ 目前旧的搜索不支持分页
    const pageSize = searchOption.displayMode === DISPLAY_MODE.OLD ? 100 : 200;
    if (
        state.inputGroup.name ||
        state.inputGroup.accommodation ||
        searchOption.displayMode === DISPLAY_MODE.OLD
    ) {
        // 新模式下name为空不会派发'search'因为数据
        searchByPage(1, pageSize);
    }
};

// 楼层
let isShowSearch = computed(() => {
    const { searchOption = {} } = activeTab.value;
    return !!searchOption.show;
});

// 面包屑点击
const handleBreadcrumb = (row = {}, index = 0) => {
    if(index==0){
        return
    }    
    // 特需处理下 根据参数的值禁止点击面包屑 因为只能选当前部门下的人 不存在其他部门下的人
    if (props.canClickBreadcrumbs) return;
    state.breadcrumbs.splice(index + 1);
    state.inputGroup.name = "";
    state.inputGroup.accommodation = "";
    const tabId = activeTab.value.id;
    const options = {
        index,
        // 触发标识
        trigger: TRIGGER_MAP.BREAD_CRUMBS,
    };
    emit("toggleLevel", tabId, toRaw(row), options);
};

// tab切换
const onTabsChange = () => {
    emit("toggleTabs", activeTab.value);
    state.inputGroup.name = "";
    state.inputGroup.accommodation = "";
    state.isSearchTable = false;
    // 初始化面包屑
    state.breadcrumbs = [
        {
            name: activeTab.value.tab,
            id: activeTab.value.id,
        },
    ];
};
// 禁止下一步
const prohibitNextStep = computed(() => {
    return (ID) => {
        if (ID && state.selectedList?.length) {
            const idex = state.selectedList.findIndex((v) =>
                v.userId ? v.userId == ID : v.id == ID
            );
            return idex !== -1;
        }
        return false;
    };
});
// 下级
const handleMore = (e, row) => {
    e.preventDefault();
    state.inputGroup.name = "";
    state.inputGroup.accommodation = "";
    const isChecked = state.breadcrumbs.some((v) => v.id === row.id);
    !isChecked && state.breadcrumbs.push(row);
    const tabId = activeTab.value.id;
    const options = {
        index: state.breadcrumbs.length - 1,
        // 触发标识
        trigger: TRIGGER_MAP.NEXT_MORE,
    };
    emit("toggleLevel", tabId, toRaw(row), options);
};
// 由父级定位面包层级
const positioningHierarchy = (row, dataSource) => {
    state.breadcrumbs = state.breadcrumbs.concat(row.reverse());
    modelState.dataSource = dataSource;
};
// 左侧选中
const originCheckedList = computed(() => {
    return activeTab.value.single
        ? state.selectedList[0]?.id
        : state.selectedList.map((item) => item.id);
});

// 全选
const checkAll = computed(() => {
    if (activeTab.value.single) {
        return false;
    }
    let selectableList = [];
    if ([SELECT_TYPE.DEPARTMENT, SELECT_TYPE.ALL].includes(props.type)) {
        selectableList = modelState.dataSource;
    } else if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.CLASS].includes(props.type)) {
        const value = activeTab.value.personField?.value || [];
        // 获取到人|班级|宿舍的列表
        selectableList = modelState.dataSource.filter(
            (item) =>
                value.includes(item[activeTab.value.personField?.key]) ||
                item.hasOwnProperty(activeTab.value.personField?.key)
        );
    }
    return (
        !!selectableList.length &&
        selectableList.every(
            (item) => originCheckedList.value.indexOf(item.id) > -1
        )
    );
});

// 是否展示下一级
const isShowMore = computed(() => {
    return (item) => {
        const _type = [
            SELECT_TYPE.PEOPLE,
            SELECT_TYPE.CLASS,
            SELECT_TYPE.ALL,
        ].includes(props.type);
        if (props.canClickBreadcrumbs) {
            return;
        }
        if (_type) {
            // 或者直接判断对象是否存在改字段（例如：userId  人员特有的）
            // item.hasOwnProperty(tab.personField?.key)
            // 人|班级|宿舍
            const value = activeTab.value.personField?.value || [];
            const isPerson =
                value.includes(item[activeTab.value.personField?.key]) ||
                item.hasOwnProperty(activeTab.value.personField?.key);
            return !isPerson;
        }
        return item.children?.length;
    };
});

// 全选事件
const onCheckAllChange = (e) => {
    let selectableList = [];
    if ([SELECT_TYPE.DEPARTMENT, SELECT_TYPE.ALL].includes(props.type)) {
        selectableList = modelState.dataSource;
    } else if ([SELECT_TYPE.CLASS, SELECT_TYPE.PEOPLE].includes(props.type)) {
        // 或者直接判断对象是否存在改字段（例如：userId  人员特有的）
        // item.hasOwnProperty(tab.personField?.key)
        const value = activeTab.value.personField?.value || [];
        selectableList = modelState.dataSource.filter(
            (item) =>
                value.includes(item[activeTab.value.personField?.key]) ||
                item.hasOwnProperty(activeTab.value.personField?.key)
        );
    }
    //  $ 用于区分tab,记录类型
    const _type = activeTab.value.id;
    if (e.target.checked) {
        const selected = state.selectedList.map(
            (item) => item[props.fieldNames.value]
        );
        selectableList.forEach((item) => {
            if (!selected.includes(item[props.fieldNames.value]) && !item.disabled) {
                state.selectedList.push({ ...item, _type });
            }
        });
    } else {
        const ids = new Set(
            selectableList.map((item) => item[props.fieldNames.value])
        );
        // 过滤在ids存在的
        state.selectedList = state.selectedList.filter(
            (item) => !ids.has(item[props.fieldNames.value])
        );
    }
};

// 滚动加载
const handleScroll = (event) => {
    // 滚动区域的高度 - 滚动条到顶部高度
    // isShowSearch.value为true 是带输入框 则高度为400 否则为440
    const isScroll =
        event.target.scrollHeight - event.target.scrollTop <
        (isShowSearch.value ? 400 : 440);
    // modelState.loading = true;
    // if (modelState.isPpresent && isScroll) {
    if (isScroll) {
        // modelState.isPpresent = false;
        // const pageNo = modelState.searchTable.pageNo + 1;
        // emit("onScroll", pageNo);
        // 初始的数量
        const _dataSLen = modelState._dataSource.length;
        const _dataSLens = _dataSLen + 10;
        // 根据初始的数量开始 到_dataSLens加10后截取
        modelState._dataSource = [
            ...modelState._dataSource,
            ...modelState.dataSource.slice(_dataSLen, _dataSLens),
        ];
    }
};

// 单选
const onCheckChange = (e, row) => {
    //  $ 用于区分tab,记录类型
    const _type = activeTab.value.id;
    if (activeTab.value.single) {
        // 单选
        state.selectedList = e.target.checked ? [{ ...row, _type }] : [];
    } else {
        // 复选
        if (e.target.checked) {
            state.selectedList.push({ ...row, _type });
        } else {
            const index = state.selectedList.findIndex(
                (item) =>
                    item[props.fieldNames.value] == row[props.fieldNames.value]
            );
            ~index && state.selectedList.splice(index, 1);
        }
    }
};

// 清空
const clearAllSelected = () => {
    state.selectedList = [];
    state.selectedRowKeys = [];
};

// 删除
const deleteSelectedItem = (index) => {
    state.selectedList.splice(index, 1);
    state.selectedRowKeys.splice(index, 1);
};

// 取消
const onCancel = () => {
    state.inputGroup.name = "";
    state.inputGroup.accommodation = "";
    state.isSearchTable = false;
    clearAllSelected();
    modelState.open = false;
    // $ 恢复第一层数据。新的接口是每一层都会请求的. 每次重新打开 以新状态展示
    // 恢复面包屑
    state.breadcrumbs = [state.breadcrumbs[0]];
    // 重置恢复选中的tab
    activeTabIndex.value = props.tabs.findIndex((item) => item.checked) || 0;
    emit("cancel");
};

// 确认
const onOk = () => {
    const selectedList = JSON.parse(JSON.stringify(state.selectedList));
    emit("submit", selectedList);
    onCancel();
};

// *********************
// Watch Function
// *********************

const relations = computed(() => {
    return (item) => {
        let text = "";
        store.state.selectSource.dictionary.relations.forEach(
            (j) => j.value == item && (text = j.label)
        );
        return text;
    };
});
watch(
    () => modelState.open,
    (val) => {
        if (val) {
            state.selectedList = JSON.parse(JSON.stringify(props.selected));
        }
    }
);

watch(
    () => props.tabs,
    (val) => {
        state.breadcrumbs[0].name = val[0].tab;
    },
    {
        deep: true,
    }
);
// 监听左侧数据变化 并取六个作为显示
watch(
    () => modelState.dataSource,
    (val) => {
        if (val?.length > 6) {
            modelState._dataSource = val.slice(0, 10);
            modelState.isPpresent = true;
        } else {
            modelState._dataSource = val;
        }
    },
    {
        deep: true,
    }
);
defineExpose({
    modelState,
    positioningHierarchy,
});
</script>

<style lang="less" scoped>
.mSelect-wrap {
    display: flex;
    height: 540px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);

    .section {
        display: flex;
        flex-direction: column;
        width: 50%;
        padding: 16px 0;

        &:last-child {
            border-left: 1px solid #f0f0f0;
        }

        &.active {
            .select-wrap {
                margin-top: 0;
                border-top: none;
            }
        }

        .reset-input-search {
            height: 20px;
            padding: 0px 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            &.active {
                height: 60px;
                padding-top: 18px;
            }
        }
    }
    .select-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0 16px;
        overflow: hidden;
        position: relative;
    }

    .ant-breadcrumb {
        :deep(ol) {
            display: inline-block;
        }

        li {
            display: inline;
        }

        :deep(.ant-breadcrumb-link) {
            display: inline;
        }

        span:last-child {
            pointer-events: none;
        }
    }

    .tabs {
        border-bottom: 1px solid #f0f0f0;
        padding: 0 16px 12px;

        :deep(.ant-radio-group) {
            display: flex;
            text-align: center;
        }

        :deep(.ant-radio-button-wrapper) {
            flex: 1;

            &:first-child {
                border-top-left-radius: 40px;
                border-bottom-left-radius: 40px;
            }

            &:last-child {
                border-top-right-radius: 40px;
                border-bottom-right-radius: 40px;
            }
        }
    }

    .spinning {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 999;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .structures {
        flex: 1;
        overflow-y: auto;
        margin-right: -10px;

        :deep(.ant-spin-nested-loading) {
            height: 100%;
        }

        :deep(.ant-spin-container) {
            height: 100%;
        }

        .row {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 0 8px;
            border-radius: 4px;
            margin-bottom: 6px;
            width: 100%;

            &:hover,
            &:active {
                background-color: #f6f6f6;
            }

            .tree-item {
                flex: 1;
                flex-direction: column;
            }

            .tree-item-eltern {
                display: flex;
                flex-direction: column;
                margin-left: 24px;

                .eltern-item {
                    display: flex;
                    align-items: center;
                    padding-bottom: 6px;

                    .cnt {
                        color: #999;
                    }
                }
            }
        }

        .check {
            display: flex;
            align-items: center;
            padding: 6px 0;
            width: 100%;

            :deep(span:nth-of-type(2)) {
                flex: 1;
            }
        }

        .check-visible {
            pointer-events: none;

            :deep(.ant-radio),
            :deep(.ant-checkbox) {
                visibility: hidden;
            }
        }

        .cnt {
            flex: 1;
            max-width: 170px;
            margin-left: 8px;

            .identity {
                border: 1px solid var(--primary-color);
                color: var(--primary-color);
                border-radius: 5px;
                padding: 0 4px;
                font-size: 10px;
            }
        }

        :deep(.ant-radio + span),
        :deep(.ant-checkbox + span) {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .sub {
            color: #999;
        }

        .more {
            font-size: 14px;
            color: var(--primary-color);
            line-height: 16px;
            padding-left: 12px;
            border-left: 1px solid #d9d9d9;
            margin-left: auto;
            min-width: 42px;
            cursor: pointer;
            user-select: none;

            .more-next {
                &.acitve {
                    color: #999999;
                }
            }
        }
    }

    :deep(.ant-avatar) {
        font-size: 14px;
        background: var(--primary-color);
    }

    :deep(.ant-avatar-image) {
        background: transparent;
    }
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}

.data-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 90px;

    img {
        width: 201px;
        height: 117px;
    }

    p {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        line-height: 26px;
        text-align: center;
        margin-top: 17px;
    }
}
</style>
