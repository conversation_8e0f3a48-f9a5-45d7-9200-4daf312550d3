<template>
    <div class="login_box">
        <div class="introduction">
            <img src="@/assets/images/pic1.png" alt="logo" />
        </div>
        <div class="form_box">
            <p class="form_title" v-if="state.showArea !== SHOW_AREA_MAP.PASSWORD">{{config.title}}</p>
            <!-- 登录 -->
            <LoginContent
                v-if="state.showArea === SHOW_AREA_MAP.LOGIN"
                :loading="state.loadingBtn"
                @submit="submitLogin"
                :loginText="state.loginText"
            />
            <!-- 重置密码 -->
            <Password v-else-if="state.showArea === SHOW_AREA_MAP.PASSWORD" @submit="submitLogin" />
        </div>
        <div class="copyright">
            <span v-if="config.copyright">{{config.copyright}}</span>
            <a v-if="config.icp" href="https://beian.miit.gov.cn/#/Integrated/recordQuery" target="_blank">{{ config.icp }}</a>
        </div>
    </div>
</template>

<script setup>
import LoginContent from './loginContent.vue'
import Password from './Password.vue'
import config from "@/config"
import RSA from '@/utils/rsa.js'
import { useRouter } from 'vue-router'
const router = useRouter()
const store = useStore()

// 显示区域类型
const SHOW_AREA_MAP = {
    // 登录
    LOGIN: 'login',
    // 修改密码
    PASSWORD: 'password',
}

const state = reactive({
    showArea: SHOW_AREA_MAP.LOGIN, // 界面显示的区域
    isLogin: false, // 是否已经登录（登录过后选学校）
    loginText: '登 录', // 按钮文案
    loadingBtn: false, // 按钮加载状态
    schoolList: [], //学校列表
    accessToken: null, // token
})

// 登录状态更新
const updateLoginStatus = status => {
    switch (status) {
        case 'success': {
            state.loginText = '登录'
            state.loadingBtn = false
            break
        }
        case 'error': {
            state.loginText = '登录失败'
            state.loadingBtn = false
            store.clearUser()
            setTimeout(() => {
                state.loginText = '登录'
            }, 2000)
            break
        }
        case 'loading': {
            state.loginText = '登录中...'
            state.loadingBtn = true
            break
        }
    }
}

// 获取用户信息
// async function getCurrentUser() {
//     const res = await http.get('/marketing/adminUser/getCurrentUser')
//     if (res.code !== 0) {
//         updateLoginStatus('error')
//         return
//     }
//     store.userInfo = res.data
//     return res.data
// }

// 检验当前登录用户
// async function checkUserLogin(userInfo) {
//     try {
//         updateLoginStatus('loading')
//         const { corporation } = userInfo
//         // TODO: 目前写死取第一个，后续如果有多个企业需求，登录需要追加跟云平台一样有个选择学校的界面
//         const params = {
//             corpId: corporation[0].id,
//         }
//         const res = await http.get('/marketing/adminUser/checkUserLogin', params)
//         if (res.code !== 0) {
//             updateLoginStatus('error')
//             return
//         }
//         updateLoginStatus('success')
//         window.location.href = '/'
//     } catch (error) {
//         updateLoginStatus('error')
//     }
// }

// 检查用户是否退出
// async function checkUserLogout() {
//     return new Promise(async resolve => {
//         const res = await http.get('/marketing/adminUser/checkUserLogout')
//         if (res.code !== 0) {
//             updateLoginStatus('error')
//             YMessage.error(res.message)
//             resolve(false)
//             return
//         }
//         const { isLogout, logoutTime, isInitializePwd } = res.data
//         // !!目前没有注销逻辑，isLogout一直为false
//         if (isLogout) {
//             Modal.confirm({
//                 title: `提示`,
//                 icon: createVNode(ExclamationCircleFilled),
//                 content: `您的账号已于${logoutTime}提交注销申请，需要撤销后才可登录，是否撤销？`,
//                 okText: '确认',
//                 cancelText: '取消',
//                 onCancel() {
//                     resolve({
//                         isInitializePwd,
//                         isLogin: true,
//                     })
//                 },
//                 onOk() {
//                     http.get('/marketing/adminUser/cancelLogout')
//                     resolve({
//                         isInitializePwd,
//                         isLogin: false,
//                     })
//                 },
//             })
//         } else {
//             resolve({
//                 isInitializePwd,
//                 isLogin: true,
//             })
//         }
//     })
// }

// 登录按钮
async function submitLogin(accountFrom) {
    try {
        state.showArea = SHOW_AREA_MAP.LOGIN
        updateLoginStatus('loading')
        
         const p = {
            ...accountFrom,
            grant_type: "password",
            client_id: "yide-cloud",
            client_secret: "yide1234567",
         }
        
        const passWadData = {
            paramEncipher: RSA.encrypt(JSON.stringify(p)),
        }        
        
        const res = await http.form('/auth/oauth/token', passWadData)

        
        if (res.code !== 0) {
            updateLoginStatus('error')
            return
        }
        const { accessToken } = res.data        
        store.setToken(accessToken)        
        const userData = await http.get('/cloud/user/getCurrentUser')
        const user = userData.data
        // $ 直接赋值userInfo并且跳转到首页

        // debugger
        store.setUserInfo(user)

        window.location.href = "/"
        // window.location.reload()
        // debugger
        // router.push({
        //     name: 'home',
        // })
        // const { isLogin, isInitializePwd } = await checkUserLogout()
        // if (isLogin) {
        //     if (isInitializePwd) {
        //         state.showArea = SHOW_AREA_MAP.PASSWORD
        //         // 恢复控件按钮状态
        //         updateLoginStatus('success')
        //         return
        //     }
        //     const userInfo = await getCurrentUser()
        //     await checkUserLogin(userInfo)
        updateLoginStatus('success')
        // }
    } catch (error) {
        console.log(error)

        updateLoginStatus('error')
    }
}
</script>

<style lang="less" scoped>
.login_box {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100vw;
    height: 100vh;
    background: url('@/assets/images/pic-bg.png') no-repeat center;
    background-size: 100% 100%;
    padding-left: 110px;
    .introduction {
        display: flex;
        display: flex;
        align-items: center;
        img {
            width: 632px;
            height: 570px;
        }
    }
    .form_box {
        background: #ffffff;
        width: 380px;
        height: 416px;
        padding: 32px;
        box-shadow: 0px 7px 16px 0px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        position: absolute;
        top: 50%;
        left: 75%;
        transform: translate3d(-50%, -50%, 0);
        .form_title {
            text-align: left;
            font-size: 22px;
            font-weight: 800;
            margin: 22px 0px 0px 0px;
        }
    }
    .copyright {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        font-weight: 400;
        color: rgba(102, 102, 102, 0.88);
        a{
            color: rgba(102, 102, 102, 0.88);
        }
    }
}
</style>
