<template>
    <div class="reset-from">
        <div class="reset_title">设置新密码</div>
        <div class="reset_tips">为保护您的账号安全及隐私，请设置新密码</div>
        <a-form :model="state.resetFrom" ref="resetFromRef" name="resetFrom" autocomplete="off">
            <a-form-item
                name="password"
                :rules="[
                    {
                        required: true,
                        validator: verifyPassword,
                        trigger: 'blur',
                    },
                ]"
            >
                <a-input-password
                    v-model:value.trim="state.resetFrom.password"
                    placeholder="请输入包含字母、数字，长度为6-20个字符的密码"
                />
            </a-form-item>
            <a-form-item
                name="againPassword"
                :rules="[
                    {
                        required: true,
                        validator: verifyAgainPassword,
                        trigger: 'blur',
                    },
                ]"
            >
                <a-input-password
                    :disabled="!state.resetFrom.password"
                    v-model:value.trim="state.resetFrom.againPassword"
                    placeholder="请再次输入密码"
                />
            </a-form-item>
            <a-form-item>
                <a-button class="confirm-btn" type="primary" @click="resetPassword" :loading="state.loading">
                    确定
                </a-button>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import RSA from '@/utils/rsa.js'

const store = useStore()

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['submit'])

const resetFromRef = ref(null)

const state = reactive({
    resetFrom: {
        password: '',
        againPassword: '',
    },
    loading: false,
})
// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

// 自定义校验
const verifyPassword = (_rule, value) => {
    const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/
    if (!regex.test(value)) {
        return Promise.reject(new Error('请输入包含字母、数字，长度为6-20个字符的密码'))
    }
    return Promise.resolve()
}

const verifyAgainPassword = (_rule, value) => {
    if (value !== state.resetFrom.password) {
        return Promise.reject(new Error('两次输入密码不一致!'))
    }
    return Promise.resolve()
}

// 设置新密码
const resetPassword = async () => {
    resetFromRef.value.validate().then(async () => {
        try {
            state.loading = true
            const passData = {
                paramEncipher: RSA.encrypt(
                    JSON.stringify({
                        newPwd: state.resetFrom.password,
                        confirmPwd: state.resetFrom.againPassword,
                    }),
                ),
            }
            const result = await http.post('/marketing/adminUser/updateNewPassword', passData)
            YMessage.success(result.message)
            const account = { ...store.accountFrom, password: state.resetFrom.password }
            store.accountFrom = account
            emit('submit', account)
        } catch (error) {
            YMessage.error(error.message)
        } finally {
            state.loading = false
        }
    })
}
</script>

<style lang="less" scoped>
.reset-from {
    .reset_title {
        font-size: 26px;
        font-weight: 600;
        color: #000000;
        margin: 18px 0 8px;
    }

    .reset_tips {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        padding-bottom: 60px;
    }

    :deep(.ant-form-item) {
        margin-bottom: 30px;
        input {
            height: 30px;
        }
    }

    .confirm-btn {
        width: 100%;
        height: 40px;
        margin-top: 20px;
    }
}
</style>
