<template>
    <div class="login_con">
        <!-- 账号密码登录 -->
        <a-form ref="loginFormRef" :model="accountFrom" name="accountFrom" class="accountFrom">
            <a-form-item
                name="username"
                :rules="[{ required: true, trigger: 'blur', message: '请输入你的手机号或用户名' }]"
            >
                <div>
                    <a-input
                        size="large"
                        v-model:value.trim="accountFrom.username"
                        placeholder="请输入你的手机号或用户名"
                    />
                </div>
            </a-form-item>
            <a-form-item name="password" :rules="[{ required: true, trigger: 'blur', message: '请输入密码' }]">
                <a-input-password
                    size="large"
                    v-model:value.trim="accountFrom.password"
                    placeholder="请输入密码"
                    @keydown="handleKeydown"
                    autocomplete="on"
                />
            </a-form-item>
            <a-form-item class="password_operate">
                <a-checkbox v-model:checked="store.remember">
                    <span class="passText">记住密码</span>
                </a-checkbox>
            </a-form-item>
            <a-form-item mb-24 mt-38>
                <a-button size="large" m-0 style="width: 100%" type="primary" @click="submit" :loading="loading">
                    {{ loginText }}
                </a-button>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup name="accountFrom">
const store = useStore()
const emit = defineEmits(['submit'])

// *********************
// Hooks Function
// *********************
const props = defineProps({
    // 按钮文案
    loginText: {
        type: String,
        default: '登 录',
    },
    loading: {
        type: Boolean,
        default: false,
    },
})

const loginFormRef = ref(null)

const accountFrom = ref({
    username: '',
    password: '',
    // grant_type: 'password',
    // client_id: 'yide-marketing',
    // client_secret: 'yide1234567',
})

const loading = computed(() => {
    return props.loading
})
const loginText = computed(() => {
    return props.loginText
})

// *********************
// Life Event Function
// *********************
onMounted(() => {
    if (store.remember) {
        // 获取缓存账号密码
        const { username, password } = store.accountFrom
        accountFrom.value.username = username
        accountFrom.value.password = password
    }
})

// *********************
// Service Function
// *********************

// 确认登录
function submit() {
    loginFormRef.value
        .validate()
        .then(() => {
            // !!!缓存账号密码数据 -修改密码的账号信息是从这里获取的
            store.accountFrom = accountFrom.value
            emit('submit', accountFrom.value)
        })
        .catch(err => {
            console.log('err: ', err)
        })
}
// 密码框回车键
function handleKeydown(e) {
    const key = e.keyCode
    if (key === 13) {
        submit()
    }
}
</script>

<style scoped lang="less">
.login_con {
    margin-top: 40px;
    .accountFrom {
        :deep(.ant-form-item) {
            &:nth-of-type(1) {
                height: 65px;
            }
            &:nth-of-type(2) {
                height: 65px;
            }
        }
    }
}
.password_operate {
    margin-bottom: 10px;
    :deep(.ant-form-item-control-input-content) {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
    }
    .passText {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 20px;
    }
}
</style>
