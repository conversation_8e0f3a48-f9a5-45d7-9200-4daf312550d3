import { createRouter, createWebHashHistory } from 'vue-router'
import config from './config'
import getRoute from './getRoute'

// !!! 如需权限路由，请查阅yd-admin项目的路由实现
const getAsRoute = async () => {
    
   let [routeArr] = await getRoute()
    console.log(routeArr)// // 没有路由就跳转登录    

    let redirectUrl = ``
    if (routeArr.length) {
        redirectUrl = routeArr[0].path
    } else {
        YMessage.warning('您还没有权限，请联系管理员')       
        routeArr = [routeArr]
        setTimeout(() => {
            // window?.close()
        }, 2000)
    }

    const router = [
        {
            path: '/',
            redirect: '/home',
        },
        {
            name: 'main',
            path: '/',
            component: () => import('@/components/layout/index.vue'),
            children: [
                ...routeArr,
                {
                    path: '/404',
                    name: '404',
                    component: () => import('@/components/common/404.vue'),
                },
                {
                    path: '/no-auth',
                    name: 'no-auth',
                    component: () => import('@/components/common/NoAuth.vue'),
                },
                {
                    path: '/redirect',
                    name: 'redirect',
                    component: () => import('@/components/common/redirect.vue'),
                },
            ],
        },
        //  调试
        {
            path: '/paperDesign',
            component: () => import('@/pages/paperDesign/index.vue'),
        },
    ]
    return router
}

const init = async () => {
    let routes = [
        {
            path: '/login',
            name: 'login',
            component: () => import('@/components/login/index.vue'),
        },
        {
            path: '/:pathMatch(.*)',
            redirect: '/404',
        },
    ]
    if (window.location.hash.indexOf('login') == -1) {
        const arr = await getAsRoute() || []
        routes = [...routes, ...arr]
    }
    return Promise.resolve(routes)
}

const asRouter = async () => {
    return new Promise((resolve, reject) => {
        init().then(res => {
            const router = createRouter({
                history: createWebHashHistory(),
                scrollBehavior() {
                    return { top: 0, left: 0 }
                },
                routes: res,
            })
            config(router)
            return resolve(router)
        })
    })
}

export default asRouter
