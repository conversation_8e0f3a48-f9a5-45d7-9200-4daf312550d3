

import {useStore} from '@/store'
import http from '@/utils/http'
const store = useStore()





export default router => {
    router.beforeEach(async (to, from, next) => {
        if (to.query.token) {
            store.setToken(to.query.token)
            try {
                const res = await http.get('/cloud/user/getCurrentUser')
                if (res.code == 0) {
                    const user = res.data

                    console.log(user)

                    store.setUserInfo(user)
                }
            } catch (error) {
                console.log(error)
                store.clearUser()
                console.log(to)
                 router.push({path: '/login'})
            }
            // router.push({path: to.path})
        };
        next()
    })
    router.afterEach((to, from) => {
        
    })
}
