// export const routeConfigs = [
//     {
//         path: '/home',
//         name: 'home',
//         meta: {
//             title: '首页',
//             icon: 'icon-nav_tsxt_nor',
//         },
//         component: () => import('@/pages/home/<USER>'),
//     },
//     {
//         path: '/dispatch',
//         name: 'dispatch',
//         meta: {
//             title: '发文拟稿',
//             icon: 'icon-nav_tsxt_nor',
//         },
//         component: () => import('@/pages/draft/dispatch.vue'),
//     },
//     {
//         path: '/draft-operate',
//         name: 'draftOperate',
//         meta: {
//             title: '拟稿操作',
//             icon: 'icon-nav_tsxt_nor',
//             hiddenMenu: true,
            
//         },
//         component: () => import('@/pages/draftOperate/index.vue'),
//     },
//     {
//         path: '/receiving',
//         name: 'receiving',
//         meta: {
//             title: '收文登记',
//             icon: 'icon-nav_tsxt_nor',
//         },
//         component: () => import('@/pages/draft/receiving.vue'),
//     },
//     {
//         path: '/signoff',
//         name: 'signoff',
//         meta: {
//             title: '签报拟稿',
//             icon: 'icon-nav_tsxt_nor',
//         },
//         component: () => import('@/pages/draft/signoff.vue'),
//     },
//     //
//     {
//         path: '/official-docs',
//         name: 'official-docs',
//         meta: {
//             title: '公文管理',
//             icon: 'icon-nav_tsxt_nor',
//         },
//         children: [
//             {
//                 path: '/official-docs/dispatch',
//                 name: 'official-docs-dispatch',
//                 meta: {
//                     title: '发文管理',
//                 },
//                 component: () => import('@/pages/officialDocs/dispatch.vue'),
//             },
//             {
//                 path: '/official-docs/receiving',
//                 name: 'official-docs-receiving',
//                 meta: {
//                     title: '收文管理',
//                 },
//                 component: () => import('@/pages/officialDocs/receiving.vue'),
//             },
//             {
//                 path: '/official-docs/signoff',
//                 name: 'official-docs-signoff',
//                 meta: {
//                     title: '签报管理',
//                 },
//                 component: () => import('@/pages/officialDocs/signoff.vue'),
//             },
//         ],
//     },
//     {
//         path: '/official-docs-store',
//         name: 'official-docs-store',
//         meta: {
//             title: '公文库',
//             icon: 'icon-nav_tsxt_nor',
//         },
//         children: [
//             {
//                 path: '/official-docs-store/dispatch',
//                 name: 'official-docs-store-dispatch',
//                 meta: {
//                     title: '发文库',
//                 },
//                 component: () => import('@/pages/officialDocsStore/dispatch.vue'),
//             },
//             {
//                 path: '/official-docs-store/receiving',
//                 name: 'official-docs-store-receiving',
//                 meta: {
//                     title: '收文库',
//                 },
//                 component: () => import('@/pages/officialDocsStore/receiving.vue'),
//             },
//             {
//                 path: '/official-docs-store/signoff',
//                 name: 'official-docs-store-signoff',
//                 meta: {
//                     title: '签报库',
//                 },
//                 component: () => import('@/pages/officialDocsStore/signoff.vue'),
//             },
//             {
//                 path: '/official-docs-store/sealup',
//                 name: 'official-docs-store-sealup',
//                 meta: {
//                     title: '归档库',
//                 },
//                 component: () => import('@/pages/officialDocsStore/sealup.vue'),
//             },
//         ],
//     },
// ]


