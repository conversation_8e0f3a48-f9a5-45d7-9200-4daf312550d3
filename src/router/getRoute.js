const store = useStore()
const modules = import.meta.glob('../pages/**/index.vue', { import: 'default' })

// 隐藏路由名单
let Perms = []
const setRouter = arr => {
    let list = []
    arr?.forEach(i => {
        let item = {
            path: i.path,
            name: i.component,
            meta: {
                title: i.name,
                icon: i.icon,
                // 是否显示在左侧菜单中
                isTreeMenu: i.isTreeMenu,
            },
        }
        if (i.children && i.children.length > 0) {
            item.children = setRouter(i.children)[0]
        }
        if (i.filePath) {
            item.component = modules[i.filePath]
        }
        if (i.type == 'C') {
            list.push(item)
        }
        if(i.redirect){
            item.redirect = i.redirect
        }
    })
    return [list]
}
const setPerms = arr => {
    arr?.forEach(i => {
        if (i.perms) {
            Perms.push(i.perms)
        }

        setPerms(i.btnList || [])
    })
}
let routerLists = null
const whiteList = ['recommend', 'login']
const isWhite = () => {
    const url = window.location.href
    let flag = false
    whiteList.forEach(i => {
        if (url.indexOf(i) != -1) {
            flag = true
        }
    })
    return flag
}
const lauyChildren = arr => {
    arr?.forEach(i => {
        store.isAlarmList = i.component == 'alarmReceive'
        if (i.btnList.length) {
            setPerms(i.btnList || [])
        }
        if (i.children.length) {
            lauyChildren(i.children)
        }
    })
}
export default async () => {
    // if (!store.token || isWhite()) {
    //     return new Promise(r => {
    //         // window.location.href = '/#/login'
    //         r([[], []])
    //     })
    // } else {
    return new Promise((resolve, reject) => {
        if (routerLists) {
            resolve(routerLists)
        } else {
            let params = getUrlParams()
            if (params.token) {
                store.setToken(params.token)
            }
            // 获取动态路由
            http.get('/system/menu/getRouters', {}, { Platform: 'antiBullying' })
                .then(async res => {
                    let data = res.code !== 0 ? [] : res?.data
                    const routerList = setRouter(data)
                    if (data.length) {
                        lauyChildren(data[0].children)
                        store.Perms = Perms
                    }
                    routerLists = routerList
                    resolve(routerList)
                })
                .catch(error => {
                    resolve([
                        {
                            path: '/:pathMatch(.*)',
                            redirect: '/404',
                        },
                    ])
                })

            return
        }
    })
    // }
}
