import { createApp } from 'vue'
import App from './App.vue'
import pinia from '@/store/pinia'
import 'uno.css'
import '@/assets/css/common.less'
import '@/assets/css/antd.less'

const app = createApp(App)
directive(app)
app.use(pinia)

app.directive('autofocus', {
  mounted(el) {
    el.focus();
  }
});

const init = async () => {
    
    import('@/router/index.js').then(async res => {
        const router = await res.default()
        app.use(router).mount('#app')
    })
    
}
init()

