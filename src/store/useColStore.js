import { defineStore } from 'pinia'
export default defineStore('Dormitory', {
    state: () => {
        return {
            data: {},
            setting: {},
        }
    },
    persist: {
        enabled: true,
        key: 'col',
        storage: localStorage,
    },
    actions: {
        set(key, columns) {
            this.data[key] = columns
        },
        get(key) {
            return this.data[key]
        },
        setSetting(key, list) {
            this.setting[key] = list
        },
        getSetting(key) {
            return this.setting[key]
        },
    },
})
