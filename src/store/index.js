import { defineStore } from 'pinia'

export const useStore = defineStore('main', {
    state: () => {
        return {
            userInfo: {},
            token: null,
            spinning: false,
            remember: true,
            accountFrom: {},
            Perms: [],
            tags: [
                {
                    key: 'home',
                    title: '首页',
                },
            ],
            collapsed: false,
            exTag: '',

            sizeOptions:[
                { value: 14, label: '14px' },
                { value: 16, label: '16px' },
                { value: 18, label: '18px' },
                { value: 20, label: '20px' },
            ],
            fileTypeOptions:[
                { value: 'RULE_FILE', label: '制度文件' },
                { value: 'MEETING_SUMMARY', label: '会议纪要' },
                { value: 'PERSONNEL_TRANSFER', label: '人事任免' },
                { value: 'INTERNAL_NOTICE', label: '内部通知' },
            ],
            urgencyLevelOptions:[
                { value: 'TJ', label: '特急' },
                { value: 'JJ', label: '急件' },
                { value: 'PJ', label: '平件' },
            ],
            securityOptions:[
                { value: 'secret', label: '绝密' },
                { value: 'highSecret', label: '机密' },
                { value: 'confidential', label: '秘密' },
                { value: 'ordinary', label: '普通' },
            ], 
            signoffOptions:[
                { value: 'DECISION_SIGN', label: '决策签报' },
                { value: 'FUNDS_SIGN', label: '经费签报' },
                { value: 'PERSONNEL_SIGN', label: '人事签报' },
                { value: 'AUTHORIZATION_SIGN', label: '授权签报' },
                { value: 'EMERGENCY_SIGN', label: '应急签报' },
            ],
            statusOptions:[                
                { value: 'WAIT_SUBMIT', label: '流程待提交' },
                { value: 'CHENG_BAN', label: '承办' },
                { value: 'PI_BAN', label: '批办' },
                { value: 'NI_BAN', label: '拟办' },
                { value: 'CHU_SHEN', label: '初审' },
                { value: 'DENG_JI', label: '登记' },
                { value: 'NI_GAO', label: '拟稿' },
                { value: 'GUI_DANG', label: '归档' },
                { value: 'BAN_JIE', label: '办结' },
                { value: 'YUE_DU', label: '阅读' },
                { value: 'CHUAN_YUE', label: '传阅' },
                { value: 'FEN_FA', label: '分发' },
                { value: 'QIAN_ZHANG', label: '签章' },
                { value: 'TAO_HONG', label: '套红' },
                { value: 'BIAN_HAO', label: '编号' },
                { value: 'QIAN_FA', label: '签发' },
                { value: 'HUI_QIAN', label: '会签' },
                { value: 'HE_GAO', label: '核稿' },
                { value: 'YI_CHE_XIAO', label: '撤销' },
                { value: 'YI_GUI_DANG', label: '已归档' },
            ],
            templateStatusOptions:[
                { value: false, label: '启用' },
                { value: true, label: '禁用' },
            ]
        }
    },
    persist: {
        enabled: true,
        key: 'yide',
        storage: localStorage,
        paths: ['userInfo', 'token', 'remember', 'accountFrom'],
    },
    actions: {
        setUserInfo(userInfo) {
            this.userInfo = userInfo
        },
        setToken(token) {
            this.token = token
        },
        clearUser() {
            this.userInfo = {}
            this.token = null
        },
    },
})
