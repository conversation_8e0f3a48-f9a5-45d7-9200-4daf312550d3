<template>
  <div class="toolbar">
    <div justify="center" align="center">
      <a-space>
        <a-select
          v-model:value="state.fontSize"
          style="width: 80px"
          :options="fontSizeOptions"
          @change="(e)=>handleMenuClick({key:'fontSize',value:e})"
        >
        </a-select>
        <a-select
          v-model:value="state.fontFamily"
          style="width: 120px"
          :options="fontFamilyOptions"
          @change="(e)=>handleMenuClick({key:'fontFamily',value:e})"
        >
          <template #option="{ value, label }">
            <span :style="{ fontFamily: value, fontWeight: 400 }">
              {{ label }}</span
            >
          </template>
        </a-select>

         <a-dropdown  arrow placement="bottom" :trigger="['click']">
            <a-tooltip placement="top">
                <template #title>
                <span>字体颜色</span>
                </template>
                <div class="icon-warp">
                    <FontColorsOutlined size="28"/>
                </div>
            </a-tooltip>
            <template #overlay>
               <a-menu>
                    <a-menu-item disabled style="padding: 0;">
                      <SketchPicker v-model="state.color" @update:modelValue="(val)=>handleMenuClick({key:'color',value:val})"/>
                    </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

        <a-dropdown  arrow placement="bottom" :trigger="['click']">
            <a-tooltip placement="top">
                <template #title>
                <span>背景颜色</span>
                </template>
                <div class="icon-warp">
                    <BgColorsOutlined size="28"/>
                </div>
            </a-tooltip>
            <template #overlay>
               <a-menu>
                  <a-menu-item disabled style="padding: 0;">
                    <SketchPicker v-model="state.backgroundColor" @update:modelValue="(val)=>handleMenuClick({key:'backgroundColor',value:val})"/>
                  </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        <a-divider type="vertical" />

         <a-tooltip placement="top">
            <template #title>
            <span>加粗</span>
            </template>
            <div class="icon-warp" :class="{'active':state.fontWeight == 'bold'}" @click="handleMenuClick({key:'fontWeight',value:'bold'})">
                <BoldOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-tooltip placement="top">
            <template #title>
            <span>斜体</span>
            </template>
            <div class="icon-warp" :class="{'active':state.fontStyle == 'italic'}" @click="handleMenuClick({key:'fontStyle',value:'italic'})">
                <ItalicOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-tooltip placement="top">
            <template #title>
            <span>下划线</span>
            </template>
            <div class="icon-warp" :class="{'active':state.textDecoration == 'underline'}" @click="handleMenuClick({key:'textDecoration',value:'underline'})">
                <UnderlineOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-divider type="vertical" />

        <a-tooltip placement="top">
            <template #title>
            <span>顶部对齐</span>
            </template>
            <div class="icon-warp" :class="{'active':state.verticalAlign == 'top'}" @click="handleMenuClick({key:'verticalAlign',value:'top'})">
                <VerticalAlignTopOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-tooltip placement="top">
            <template #title>
            <span>垂直居中</span>
            </template>
            <div class="icon-warp" :class="{'active':state.verticalAlign == 'middle'}" @click="handleMenuClick({key:'verticalAlign',value:'middle'})">
                <VerticalAlignMiddleOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-tooltip placement="top">
            <template #title>
            <span>底部对齐</span>
            </template>
            <div class="icon-warp" :class="{'active':state.verticalAlign == 'bottom'}" @click="handleMenuClick({key:'verticalAlign',value:'bottom'})">
                <VerticalAlignBottomOutlined size="28"/>
            </div>
        </a-tooltip>
         <a-tooltip placement="top">
            <template #title>
            <span>左对齐</span>
            </template>
            <div class="icon-warp" :class="{'active':state.textAlign == 'left'}" @click="handleMenuClick({key:'textAlign',value:'left'})">
                <AlignLeftOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-tooltip placement="top">
            <template #title>
            <span>居中</span>
            </template>
            <div class="icon-warp" :class="{'active':state.textAlign == 'center'}" @click="handleMenuClick({key:'textAlign',value:'center'})">
                <AlignCenterOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-tooltip placement="top">
            <template #title>
            <span>右对齐</span>
            </template>
            <div class="icon-warp" :class="{'active':state.textAlign == 'right'}" @click="handleMenuClick({key:'textAlign',value:'right'})">
                <AlignRightOutlined size="28"/>
            </div>
        </a-tooltip>
        <a-divider type="vertical" />        

        <a-dropdown v-model:open="state.isOpenBorder" @click="handleButtonClick" arrow placement="bottom" :trigger="['click']">
            <a-tooltip placement="top">
                <template #title>
                    <span>边框设置</span>
                </template>
                <div class="icon-warp">
                    <TableOutlined size="28"/>
                </div>
            </a-tooltip>
            <template #overlay>
                <a-menu @click="handleMenuClick">
                    <a-menu-item key="border-left">
                        <BorderLeftOutlined />
                        左边框
                    </a-menu-item>
                    <a-menu-item key="border-right">
                        <BorderRightOutlined />
                        右边框
                    </a-menu-item>
                    <a-menu-item key="border-top">
                        <BorderTopOutlined />
                        上边框
                    </a-menu-item>
                    <a-menu-item key="border-bottom">
                        <BorderBottomOutlined />
                        下边框
                    </a-menu-item>
                    <a-menu-item key="border">
                        <BorderOuterOutlined />
                        外边框
                    </a-menu-item>
                    <a-menu-item key="whole">
                        <TableOutlined />
                        全边框
                    </a-menu-item>
                    <a-menu-item key="noBorder">
                        <BorderInnerOutlined />
                        无边框
                    </a-menu-item>                    
                    <a-sub-menu key="line" >
                        <template #title>
                            <MenuOutlined />
                            <span style="padding-left: 5px;">线条</span>
                        </template>
                        <a-menu-item key="small">
                            <div style="width: 100px;height: 1px;background-color: #000;margin: 10px 0;"></div>
                        </a-menu-item>
                        <a-menu-item key="middle">
                            <div style="width: 100%;height: 2px;background-color: #000;margin: 10px 0;"></div>
                        </a-menu-item>
                        <a-menu-item key="large">
                            <div style="width: 100%;height: 3px;background-color: #000;margin: 10px 0;"></div>
                        </a-menu-item>
                    </a-sub-menu>
                    <a-sub-menu key="color" style="padding: 0;" >
                        <template #title>
                              <div style="width: 14px;height: 14px;display: inline-block;border-radius: 2px;" :style="{background:state.borderColor}"></div>
                              <span style="padding-left: 5px;">线条颜色</span>
                        </template>
                        <a-menu-item key="1" style="padding: 0;" disabled>
                            <SketchPicker v-model="state.borderColor" @update:modelValue="(val)=>handleMenuClick({key:'border-color',value:val})"/>
                        </a-menu-item>
                     </a-sub-menu>                     
                </a-menu>
            </template>
        </a-dropdown>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive,computed,} from "vue";
import {BorderInnerOutlined,BorderOuterOutlined,BorderBottomOutlined,BorderTopOutlined,BorderRightOutlined,BorderLeftOutlined,BorderOutlined,MenuOutlined,FontColorsOutlined,BgColorsOutlined,BoldOutlined,ItalicOutlined,UnderlineOutlined,AlignLeftOutlined,AlignCenterOutlined,AlignRightOutlined,VerticalAlignTopOutlined,VerticalAlignMiddleOutlined,VerticalAlignBottomOutlined,TableOutlined } from "@ant-design/icons-vue"

import { SketchPicker } from 'vue-color'
import 'vue-color/style.css';

const emit = defineEmits(['change'])

const fontSizeOptions = [
  {
    label: "12px",
    value: "12px",
  },
  {
    label: "14px",
    value: "14px",
  },
  {
    label: "16px",
    value: "16px",
  },
  {
    label: "18px",
    value: "18px",
  },
  {
    label: "20px",
    value: "20px",
  },
  {
    label: "22px",
    value: "22px",
  },
  {
    label: "24px",
    value: "24px",
  },
  {
    label: "26px",
    value: "26px",
  },
  {
    label: "28px",
    value: "28px",
  },
];

const fontFamilyOptions = [
  {
    label: "微软雅黑",
    value: "Microsoft YaHei, Arial, sans-serif",
  },
  {
    label: "宋体",
    value: 'SimSun, Times New Roman, serif',
  },

  {
    label: "楷体",
    value: "KaiTi, 楷体",
  },
  {
    label: "隶书",
    value: "LiSu, 隶书",
  },
  {
    label: "黑体",
    value: "SimHei, 黑体",
  },
];

const props = defineProps({
  style: {
    type: Object,
    default: () => ({
      fontSize:"14px",
      borderColor: '#D0CBFB',
      borderWidth: '1px',
    })
  }
})

const state = reactive({
  fontSize: "14px",
  fontFamily: "Microsoft YaHei, Arial, sans-serif",
  isOpenBorder: false,
  borderColor:"#000000",
  color:"#000000",
  backgroundColor:"#ffffff",
  verticalAlign:'',
  textAlign:'',
  textDecoration:''
});


watch(()=>props.style,(val)=>{
  // console.log(val)
  state.fontSize = val.fontSize || "14px"
  state.fontFamily = val.fontFamily || "Microsoft YaHei, Arial, sans-serif"
  state.borderColor = val.borderColor || "#000000"
  state.backgroundColor = val.backgroundColor || "#ffffff"
  state.color = val.color || "#000000"
  state.verticalAlign = val.verticalAlign
  state.textAlign = val.textAlign
  state.textDecoration = val.textDecoration,
  state.fontWeight = val.fontWeight
  state.fontStyle = val.fontStyle
},{
  deep:true
})

const fontFamily = computed(()=>{
  return state.fontFamily
})

function fn(e){
  // console.log(e)
}

// TODO:

function handleMenuClick(e){  
  const keyMap = {
    'border-left': `${props.style.borderWidth} solid ${props.style.borderColor}`,
    'border-right': `${props.style.borderWidth} solid ${props.style.borderColor}`,
    'border-top': `${props.style.borderWidth} solid ${props.style.borderColor}`,
    'border-bottom': `${props.style.borderWidth} solid ${props.style.borderColor}`,
    'border': `${props.style.borderWidth} solid ${props.style.borderColor}`,
    'whole': `${props.style.borderWidth} solid ${props.style.borderColor}`,
    'noBorder': 'none',
    '1': 'border-width',
    '2': 'border-width',
    '3': 'border-width',
    'border-color': state.selectedColor,
  }
  let key = e.key,value = keyMap[key] || e.value
  if(e.key === 'none'){
    key = 'border'
    value = 'none'
  }
  if(['small','middle','large'].includes(e.key)){
    key = 'border-width-' + e.key
    value = ['small','middle','large'].findIndex(i=>i === e.key)+1  + 'px'
  }
  emit("change",{
    key,
    value
  })
}

function handleButtonClick(){
    state.isOpenBorder = false;
}

function openColorSelect(id){
  document.getElementById(id).click();
}

watch(()=>props.style.borderColor,(val)=>{
  state.selectedColor = val
},{
  immediate: true
})

onMounted(() => {
  // document.addEventListener('mouseup', handleButtonClick);  
});

onUnmounted(()=>{
    document.removeEventListener('mouseup',handleButtonClick);
})
</script>

<style lang="less" scoped>
.toolbar {
  position: relative;
  :deep(.ant-select-selection-item) {
    font-family: v-bind(fontFamily);
  }

  .icon-warp{
     width: 28px;
     height: 28px;
     display: flex;
     align-items: center;
     justify-content: center;
     text-align: center;
     cursor: pointer;
     &:hover{
        background-color: #e8f8f3;
        border-radius: 14px;
        // border: 1px solid #00B781;
     }
     span{
        width: auto;
     }
  }
  .icon-warp.active{
    background-color: #e8f8f3;
    border-radius: 14px;
  }
  
  :deep(.ant-select-selector){
    background-color: #0000 !important;
  }
}

</style>
