<template>
    <a-modal :open="open" :title="title" @ok="handleOk" @cancel="handleCancel" :confirmLoading="state.submitLoading" :okButtonProps="{disabled:state.loadLoading}">
        <div class="container">
            <a-spin :spinning="state.loadLoading">
                <a-form :model="formState" ref="formRef" :rules="rules" layout="vertical" :label-col="{ span: 6 }" :wrapper-col="{ span: 24 }" style="padding: 0 16px;">
                    <a-form-item label="模板分组名称：" name="groupName" style="margin-bottom: 14px;">
                        <a-input v-model:value="formState.groupName" placeholder="请输入" :maxlength="30" show-count />
                    </a-form-item>
                    <a-form-item label="测试年份：" name="groupYear"  style="margin-bottom: 14px;">
                        <a-date-picker :allowClear="false" placeholder="请选择" v-model:value="formState.groupYear"
                            value-format="YYYY" format="YYYY年" picker="year">
                        </a-date-picker>
                    </a-form-item>
                    <a-form-item label="答题卡样式：" name="answerSheetType" style="margin-bottom: 14px;">
                        <a-select v-model:value="formState.answerSheetType" placeholder="请选择"
                            :disabled="state.isAnswerSheetType">
                            <a-select-option value="A3">A3</a-select-option>
                            <a-select-option value="A4">A4</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            </a-spin>
        </div>
    </a-modal>
</template>

<script setup>
import { toRaw,watch } from 'vue';
import { message } from "ant-design-vue"

const formRef = ref();
const emit = defineEmits(['update:open','submit'])

const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: "新增"
    },
    mode: {
        type: String,
        default: 'add',
        validator: (val) => ['add', 'edit', 'look'].includes(val)
    },
    getRead:Function,
    id:String,
})

// 表单校验
const rules = {
  groupName: [{ required: true, message: '请输入模板分组名称' }],
  groupYear: [{ required: true, message: '请选择测试年份' }],
  answerSheetType: [{ required: true, message: '请选择答题卡样式' }]
}

const state = reactive({
    submitLoading: false,
    loadLoading:false
})

const formState = reactive({
    groupName:'',
    groupYear:'',
    answerSheetType:''
})

const resetForm = () => {
    state.submitLoading = false
    state.loadLoading = false
    formRef.value.resetFields();
}

const validate = (cb)=>{
    formRef.value.validate().then(() => {
        cb && cb(toRaw(formState));
    }).catch(error => {
        console.log('error', error);
    });
}



watch(()=>props.open,(v)=>{   
    if(v && props.mode=='edit'){
        if(props.getRead){
            state.loadLoading = true
            props.getRead({id:props.id})?.then(res=>{
                console.log(res)
                const data = res.data || {}
                if(Object.keys(data).length && props.mode=='edit'){
                    for(let k in formState){
                        formState[k] = data[k]
                    }
                }
                state.loadLoading = false
            }).catch(err=>{
                // message.error(err?.message || err?.msg)
                state.loadLoading = false
            })
        }
    }
    if(v && props.mode=='add'){
        state.loadLoading = false
    }
},{
    important:true
})

function handleOk() {
    validate((data)=>{
        state.submitLoading = true
        emit('submit',{
            data:data,
            mode:props.mode,
            complete:(boole)=>{
                if(!boole){
                    state.submitLoading = false
                    return;
                }
                handleCancel()
            }
        })
    })
}

function handleCancel() {
    emit('update:open', false)
    resetForm()
    if(state.loadLoading){
        // 取消请求
    }
}

</script>

<style lang="less" scoped>
.container {
    position: relative;
    padding: 16px 0;
    min-height: 100px;
}
</style>