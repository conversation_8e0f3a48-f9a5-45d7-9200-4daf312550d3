<template>
    <a-modal :open="open" :title="title" @ok="handleOk" @cancel="handleCancel" :confirmLoading="state.submitLoading" :okButtonProps="{disabled:state.loadLoading}">
        <div class="container">
            <a-spin :spinning="state.loadLoading">
                <a-form :model="formState" ref="formRef" :rules="rules"  :label-col="{ span: 6 }" :wrapper-col="{ span: 24 }" style="padding: 0 16px;">
                    <a-form-item label="分类名称：" name="classifyName" style="margin-bottom: 20px;">
                        <a-input v-model:value="formState.classifyName" placeholder="请输入" :maxlength="20" show-count />
                    </a-form-item>                    
                    <a-form-item label="类别：" name="classifyType" style="margin-bottom: 20px;">
                        <a-select v-model:value="formState.classifyType" placeholder="请选择" mode="tags">
                            <a-select-option value="docDrafts">发文拟稿</a-select-option>
                            <a-select-option value="docReceipts">收文登记</a-select-option>
                            <a-select-option value="memoDrafts">签报拟稿</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            </a-spin>
        </div>
    </a-modal>
</template>

<script setup>
import { toRaw,watch } from 'vue';
import { message } from "ant-design-vue"

const formRef = ref();
const emit = defineEmits(['update:open','submit'])

const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: "新增"
    },
    mode: {
        type: String,
        default: 'add',
        validator: (val) => ['add', 'edit', 'look'].includes(val)
    },
    getRead:Function,
    id:String,
})

// 表单校验
const rules = {
  classifyName: [{ required: true, message: '请输入分类名称' }],
  classifyType: [{ required: true, message: '请选择类别' }],
}

const state = reactive({
    submitLoading: false,
    loadLoading:false
})

const formState = reactive({
    classifyName:'',
    classifyType:[]
})

const resetForm = () => {
    state.submitLoading = false
    state.loadLoading = false
    formRef.value.resetFields();
}

const validate = (cb)=>{
    formRef.value.validate().then(() => {
        cb && cb(toRaw(formState));
    }).catch(error => {
        console.log('error', error);
    });
}



watch(()=>props.open,(v)=>{   
    if(v && props.mode=='edit'){
        if(props.getRead){
            state.loadLoading = true
            props.getRead({id:props.id})?.then(res=>{
                console.log(res)
                const data = res.data || {}
                if(Object.keys(data).length && props.mode=='edit'){
                    for(let k in formState){
                        if(k=='classifyType'){
                            formState[k] = data[k].split(',')
                            continue
                        }
                        formState[k] = data[k]
                    }
                }
                state.loadLoading = false
            }).catch(err=>{
                // message.error(err?.message || err?.msg)
                state.loadLoading = false
            })
        }
    }
    if(v && props.mode=='add'){
        state.loadLoading = false
    }
},{
    important:true
})

function handleOk() {
    validate((data)=>{
        state.submitLoading = true
        data.classifyType = data.classifyType.join(',')
        emit('submit',{
            data:data,
            mode:props.mode,
            complete:(boole)=>{
                if(!boole){
                    state.submitLoading = false
                    return;
                }
                handleCancel()
            }
        })
    })
}

function handleCancel() {
    emit('update:open', false)
    resetForm()
    if(state.loadLoading){
        // 取消请求
    }
}

</script>

<style lang="less" scoped>
.container {
    position: relative;
    padding: 16px 0;
    min-height: 100px;
}
</style>