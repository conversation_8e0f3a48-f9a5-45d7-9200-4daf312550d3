<template>
    <div class="file-container">
        <!-- search -->
        <a-form layout="inline" :model="search" ref="searchRef">
            <a-form-item label="分类名称：" name="classifyName" style="margin-bottom: 16px">
                <a-input v-model:value="search.classifyName" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item style="margin-bottom: 16px">
                <a-button type="primary" @click="submitSearch">
                    <template #icon>
                        <SearchOutlined />
                    </template>查询
                </a-button>
                <a-button class="delBtn" @click="resetSearch">
                    <template #icon>
                        <RedoOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-form>
        <!-- handle -->
        <div class="handle">
            <a-button type="primary" @click="handleOpenModal('add')">新增分类</a-button>
            <!-- <a-button @click="handleBatchDelete" :disabled="!state.selectedRowKeys.length">删除</a-button> -->
        </div>
        <!-- table -->
        <a-table rowKey="id" :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: tableSelectChange }"
            :dataSource="state.list" :columns="columns" bordered class="table" :pagination="pagination"
            @change="changePagination" :loading="state.loading">
            <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'operation'">
                    <a-button type="link" @click="handleOpenModal('edit', record)">编辑</a-button>
                    <a-popconfirm title="确定删除?" @confirm="handleDelete(record)">
                        <a @click.stop>删除</a>
                    </a-popconfirm>

                    <a-button type="link" style="margin-left: 0;padding: 0 8px;" @click="enableTemp(record)">{{ record.status==0 ? '启用' : '禁用' }}</a-button>
                </template>
            </template>
        </a-table>
        <!-- modal -->
        <AddEditorModal v-model:open="state.modalOpen" :mode="state.modalMode" :getRead="API.read" :id="state.editId"
            :title="modalTitle" @submit="addOrUpdateListItem" />
        <!--  -->
    </div>
</template>

<script setup>
import { onMounted, ref, createVNode } from 'vue';
import { SearchOutlined, RedoOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue';
import AddEditorModal from './addEditor.vue';
import { message, Modal } from "ant-design-vue"


const API = {
    create: (p) => http.post('/cloud/official-doc/file-classify/create', p),
    delete: (p) => http.post('/cloud/official-doc/file-classify/del', p),
    query: (p) => http.post('/cloud/official-doc/file-classify/page', p),
    update: (p) => http.post('/cloud/official-doc/file-classify/update', p),
    read: (p) => http.post('/cloud/official-doc/file-classify/detail', p),
    enable: (p) => http.post("/cloud/official-doc/file-classify/updateStatus", p),
}

/**
 * 函数防抖
 * @param {Function} func 需要防抖的函数
 * @param {number} wait 等待时间(毫秒)
 * @param {boolean} immediate 是否立即执行
 * @return {Function} 返回防抖后的函数
 */
function debounce(func, wait = 100, immediate = false) {
    let timeout = null;

    return function () {
        const context = this;
        const args = arguments;

        // 清除之前设置的定时器
        if (timeout) clearTimeout(timeout);

        if (immediate) {
            // 如果已经执行过，不再执行
            const callNow = !timeout;
            timeout = setTimeout(() => {
                timeout = null;
            }, wait);
            if (callNow) func.apply(context, args);
        } else {
            // 延迟执行
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        }
    };
}

// https://www.antdv.com/components/form-cn#components-form-demo-basic
// https://www.antdv.com/components/table-cn

const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        align: 'center',
        customRender: row => `${row.index + 1}`,
        width: 70,
    },
    {
        title: '分类名称',
        dataIndex: 'classifyName',
        ellipsis: true,
        key: 'classifyName'
    },
    {
        title: '类别',
        dataIndex: 'classifyType',
        ellipsis: true,
        key: 'classifyType',
        customRender: ({ record }) => {
            return record.classifyTypeNameList?.join(',')
        },
    },
    {
        title: '状态',
        dataIndex: 'status',
        ellipsis: true,
        key: 'status',
        customRender: ({ record }) => {
            if (record.status == 0) {
                return "禁用";
            }
            return "启用";
        },
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        ellipsis: true,
        key: 'createTime'
    },
    {
        title: '操作',
        dataIndex: 'operation',
        align: 'left',
        fixed: 'right',
    },
];
const searchRef = ref();

const pagination = reactive({
    total: 0,
    size: '10',
    showQuickJumper: true,
    showLessItems: true,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '30', '40', '100'],
    showTotal: (total, range) => `共 total 条`,
});

const search = reactive({});

const state = reactive({
    list: [],

    pageSize: 10,
    pageNo: 1,
    loading: true,
    modalMode: 'add',
    modalOpen: false,
    isSearch: false,
    selectedRowKeys: [],
    editId: null
});

const modalTitle = computed(() => {
    const map = {
        add: '新增',
        edit: '编辑',
    };
    return map[state.modalMode];
});

const submitSearch = debounce(() => {
    state.isSearch = true
    init();
}, 500)

const resetSearch = debounce(() => {
    searchRef.value.resetFields();
    state.pageNo = 1;
    state.isSearch = false
    init();
}, 500)

async function enableTemp({ id, status }) {
    await API.enable({
        id,
        status: status==1 ? 0 : 1,
    }).then(res=>{
        message.success(res?.data?.message || res?.message || "操作成功");
        init();
    })
}

function handleOpenModal(type, item) {
    if (type == 'edit') {
        state.editId = item.id
    }
    state.modalMode = type;
    state.modalOpen = true;
}


function submitDelete(id, cb) {
    API.delete({
        id
    }).then(res => {
        message.success(res?.data?.message || res?.message || '操作成功');
        cb && cb()
    }).catch(err => {
        console.log(err)
    })
}

// function handleBatchDelete() {
//     Modal.confirm({
//         title: '提示',
//         cancelText: '取消',
//         okText: '确认',
//         icon: createVNode(ExclamationCircleFilled),
//         content: '是否确认删除？',
//         centered: false,
//         onOk() {
//             submitDelete(state.selectedRowKeys, () => {
//                 state.selectedRowKeys = []
//                 init()
//             })
//         },
//         onCancel() {
//             // 
//         }
//     })
// }

function handleDelete(item) {
    submitDelete(item.id, () => {
        init()
    })
}

function addOrUpdateListItem({ data, mode, complete }) {
    const params = {
        ...data,
    }
    if (mode == 'add') {
        API.create(params).then(res => {
            message.success(res?.data?.message || res?.message || '操作成功');
            complete(true)
            init()
        }).catch(err => {
            complete(false)
        })
    } else {
        params['id'] = state.editId
        API.update(params).then(res => {
            message.success(res?.data?.message || res?.message || '操作成功');
            complete(true)
            init()
        }).catch(err => {
            complete(false)
        })
    }
}

function tableSelectChange(data) {
    state.selectedRowKeys = data
}


function changePagination({ current, pageSize }) {
    state.pageSize = pageSize;
    state.pageNo = current;
    init()
}

async function init() {
    try {
        state.loading = true;
        let params = {
            pageSize: state.pageSize,
            pageNo: state.pageNo,
        };
        if (state.isSearch) {
            params = {
                ...params,
                ...search,
            };
        }
        const { data } = await API.query(params);
        const { list, pageNo, total } = data || {};
        state.pageNo = +pageNo || 1;
        pagination.total = +total || 0;
        state.list = list || [];

        // 如果当前只有一条删除后无数据返回上一页
        if (+pageNo > 1 && list.length == 0) {
            state.pageNo--
            init()
        }
    } catch (error) {
        console.log(error);
    } finally {
        nextTick(() => {
            state.loading = false;
        });
    }
}

onMounted(() => {
    init();
});
</script>

<style lang="less" scoped>
.file-container {
    position: relative;
    padding: 16px;

    .handle {
        display: flex;
        align-items: center;
        justify-content: end;
    }

    .table {
        padding-top: 16px;
    }
}
</style>
