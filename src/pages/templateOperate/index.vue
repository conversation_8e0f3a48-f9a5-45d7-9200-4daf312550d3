<template>
    <div class="template-container" style="overflow-y: auto;">
        <a-spin tip="Loading..." :spinning="state.loadLoading">
            <a-tabs class="tabs" v-model:activeKey="state.activeKey" centered>
                <template #leftExtra>
                    <a-button type="link" class="back-btn" @click="$router.back()">
                        <template #icon>
                            <LeftOutlined />
                        </template>
                        返回
                    </a-button>
                    <span>{{ state.query.handle == 'edit' ? '编辑' : '新增' }}模版</span>
                </template>
                <template #rightExtra>
                    <div style="padding-right:15px;">
                        <a-button type="primary" @click="submitForm" :loading="state.btnLoading">发 布</a-button> 
                    </div>
                </template>
                <a-tab-pane key="1" tab="基本信息" forceRender>
                    <BaseForm ref="baseFormRef" :data="state.baseForm"/>
                </a-tab-pane>
                <a-tab-pane key="2" tab="稿纸设置" forceRender>
                    <ManuscriptDesign ref="excelRef" :options="state.excelOption"/>
                </a-tab-pane>
                <a-tab-pane key="3" tab="流程设置" forceRender>
                    <FlowSetting :data="state.flewNode" @update="updateFlowNode"/>
                </a-tab-pane>
            </a-tabs>
        </a-spin>
    </div>
</template>

<script setup>
import {ExclamationCircleOutlined,LeftOutlined,PlusOutlined} from '@ant-design/icons-vue'
import { onMounted, reactive, createVNode,ref,toRaw } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';

import http from '@/utils/http'
import {deobfuscate} from '@/utils/common.js';
import ManuscriptDesign from "./components/manuscriptDesign/index.vue"
import FlowSetting from "./components/flowSetting/index.vue"
import BaseForm from "./components/baseForm/index.vue"

const baseFormRef = ref()
const excelRef = ref()

const route = useRoute()
const router = useRouter()

const state = reactive({
    activeKey:"1",
    query:{},
    baseForm:{},
    excelOption:{},
    flewNode:[
        // {
        //     status: 'process',
        //     title: '拟稿',
        // },
        // {
        //     status: 'wait',
        //     title: '核稿',           
        // },
        // {
        //     status: 'wait',
        //     title: '会签',            
        // },
        // {
        //     status: 'wait',
        //     title: '签发',            
        // },
        // {
        //     status: 'wait',
        //     title: '套红',
        // },
        // {
        //     status: 'wait',
        //     title: '签章',
        // },
        // {
        //     status: 'wait',
        //     title: '分发',
        // },
        // {
        //     status: 'wait',
        //     title: '办理',
        // },
        // {
        //     status: 'wait',
        //     title: '传阅',
        // },
        // {
        //     status: 'wait',
        //     title: '阅读',
        // },
        // {
        //     status: 'wait',
        //     title: '办结',
        // },
        // {
        //     status: 'wait',
        //     title: '归档',
        // },
    ],

    btnLoading:false,
    loadLoading:false
})

function updateFlowNode(item){
    for(let flow of state.flewNode){
        if(flow.id == item.id){
            // console.log('flow',flow)
            flow.nodeStatus = item.nodeStatus
            flow.props.assignedType = item.assignedType
            flow.props.assignedUser = item.assignedUser?.map(i=>i)
            flow.props.role = item.role?.map(i=>i)
            flow.props.mode = item.mode
            if(item.endCondition){
                flow.props.leaderTop.endCondition = item.endCondition
            }
            if(item.endLevel){
                flow.props.leaderTop.endLevel = item.endLevel
            }
            if(item.level){
                flow.props.leader.level =  item.level
            }
            break;
        }
        continue;
    }

    // console.log('state.flewNode',state.flewNode)
}

function submitForm(){
    baseFormRef.value.validate((form)=>{
        // 1.
        const settings = JSON.stringify(excelRef.value.getContent())
        const params = {
            ...form,
            type:state.query.type,
            settings,
            childNodeList: toRaw(state.flewNode)
        }
        state.btnLoading = true
        let url = `/cloud/official-doc/process-template/create`
        if(state.query.handle == "edit"){
            params['id'] = state.query.id
            url = `/cloud/official-doc/process-template/update`
        }
        // console.log(params)
        http.post(url,params).then((res)=>{
            console.log(res)
            message.success("操作成功")
            router.back()
        }).finally(()=>{
            state.btnLoading = false
        })
    })
}


function init() {
    const query = deobfuscate(route.query.code)
    console.log(query)
    if(query.errorMsg){
        message.error("参数异常，请重新打开页面")
        setTimeout(() => {
            router.back()
        }, 1500)
    }else{
       state.query = query
       state.loadLoading = true
       if(query.id){
            http.get('/cloud/official-doc/process-template/get',{id:query.id}).then(res=>{                
                const {formName,remark,childNodeList,settings} = res.data
                state.baseForm.formName = formName
                state.baseForm.remark = remark
                state.flewNode = [...childNodeList]

                try {
                    state.excelOption = JSON.parse(settings)
                    console.log("excelOption__",state.excelOption)
                } catch (error) {
                    state.excelOption = {}
                }
            }).finally(()=>{
                state.loadLoading = false
            })
       }else{
        
        http.get('/cloud/official-doc/process-template/getDefaultProcess',{
            type:query.type
        }).then(res=>{
            // console.log(res.data)
            state.flewNode = res.data
        }).finally(()=>{
            state.loadLoading = false
        })
       }
    }
}

onMounted(() => {
    init()

    
})
</script>

<style lang="less" scoped>
.back-btn {
    color: #333;

    &:hover {
        color: #00b781;
    }
}

.template-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 600;


    min-height: 100%;
    background-color: #f7f8fa;

    .tabs {
        min-height: 100%;

        :deep(.ant-tabs-nav) {
            margin-bottom: 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            height: 48px;
        }

        :deep(.ant-tabs-content-holder) {
            margin-top: 48px;
        }

        :deep(.ant-tabs-nav) {
            background-color: #fff;
        }
    }

    .base-form{
        width: 794px;
        margin: 0 auto;
        background-color: #fff;
        padding: 40px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
        margin-top: 16px;
    }
}
</style>