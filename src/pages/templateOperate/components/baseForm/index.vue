<template>
    <div class="base-form">
        <a-form :model="baseForm" ref="baseFormRef" :rules="baseRules">
            <a-form-item label="模版名称" name="formName" style="margin-bottom: 20px;">
                <a-input v-model:value="baseForm.formName" show-count :maxlength="50"></a-input>
            </a-form-item>
            <a-form-item name="remark" style="margin-bottom: 20px;">
                <template #label>
                    <span style="padding-left: 9px;">模版说明</span>
                </template>
                <a-textarea v-model:value="baseForm.remark" :rows="4" show-count :maxlength="200"
                    :auto-size="{ minRows: 4, maxRows: 6 }" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>

import { onMounted, reactive, ref } from 'vue';
import { debounce } from "@/utils/common";
import { message } from 'ant-design-vue';
const baseFormRef = ref()
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    }
})

const baseForm = reactive({
    formName: "",
    remark: ""
})

watch(()=>props.data,()=>{
    baseForm.formName = props.data.formName
    baseForm.remark = props.data.remark    
},{
    deep:true,
    immediate:true
})

const baseRules = {
    formName: [
        {
            required: true,
            message: '请输入模版名称',
            trigger: 'blur'
        },
        { max: 50, message: '最多只能输入50个字符', trigger: 'blur' },
    ],
    remark: [
        { max: 200, message: '最多只能输入200个字符', trigger: 'blur', },
    ]
}

const msgTips = debounce((msg)=>{
    message.error(msg)
}, 500)

function validate(cb){
    baseFormRef.value.validate().then(res=>{
        cb && cb(res)
    }).catch(err=>{        
        const error = err.errorFields[0]
        const msg = error.errors[0]
        msg && msgTips(msg)
    })
}

defineExpose({
    validate
})

</script>

<style lang="less" scoped>
.container {
    position: relative;
}
</style>