
<template>
    <div class="setting">
        <!-- <a-tabs v-model:activeKey="state.activeKey" centered class="setting-tabs">
            <a-tab-pane key="1" tab="模板设置">
                <div style="padding:15px">
                    
                </div>
            </a-tab-pane>
            <a-tab-pane key="2" tab="字段设置">
                <div style="padding:15px" v-if="Object.keys(value).length">
                    <a-form>
                        <a-form-item label="是否必填" style="margin-bottom: 20px;">
                            <a-switch v-model:checked="value.required" />
                        </a-form-item>
                        <a-form-item label="提示文字" style="margin-bottom: 20px;">
                            <a-input v-model:value="value.placeholder" />
                        </a-form-item>
                        <a-form-item label="最大长度" style="margin-bottom: 20px;" v-if="hideLength">
                            <a-input-number v-model:value="value.maxLength" />
                        </a-form-item>
                    </a-form>
                </div>
            </a-tab-pane>
        </a-tabs> -->
        <div style="padding:15px" v-if="Object.keys(value).length">
                    <a-form>
                        <a-form-item label="是否必填" style="margin-bottom: 20px;">
                            <a-switch v-model:checked="value.required" />
                        </a-form-item>
                        <a-form-item label="提示文字" style="margin-bottom: 20px;">
                            <a-input v-model:value="value.placeholder" />
                        </a-form-item>
                        <a-form-item label="最大长度" style="margin-bottom: 20px;" v-if="hideLength">
                            <a-input-number v-model:value="value.maxlength" />
                        </a-form-item>
                    </a-form>
                </div>
   </div>
</template>

<script setup>
    import { onMounted,reactive,computed } from 'vue';
    import {fieldSettingMap} from "./core"

   const emit = defineEmits(["update:value"])

    const props = defineProps({
        value:{
            type:Object,
            default:()=>{
                return {
                   
                }
            }
        }
    })

    const hideLength = computed(()=>{
        return ["input","textarea"].includes(props.value.fieldType)
    })

    const state = reactive({
        activeKey:"2"
    })

    watch(()=>props.value,(data)=>{
        console.log(data)
    },{
        deep:true
    })


    function init(){

    }

    onMounted(()=>{
        init()
    })
</script>

<style lang="less" scoped>
   .setting{
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: #fff;
        width: 250px;
        z-index: 100;
        box-shadow: -4px 4px 6px 0 #e3e6ec;
        
        .setting-tabs{
            :deep(.ant-tabs-nav){
                position:static !important;
            }
            :deep(.ant-tabs-content-holder){
                margin-top: 0 !important;
            }
        }
    }
</style>