
<template>
    <div class="field-list">
        <a-collapse
        v-model:activeKey="state.activeKey"
        :bordered="false"
        style="background: #fff"
        ghost
    >
        <template #expandIcon="{ isActive }">
            <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
        <a-collapse-panel key="1" header="系统字段" :style="customStyle">
            <a-row :gutter="16">
                <a-col class="gutter-row" :span="12" v-for="(item,index) in state.list" :key="index">
                    <div class="gutter-box" draggable="true" 
                    @dragstart="dragStart($event,item)"
                    @dragover="dragOver"
                    @drop="drop"
                    >{{ item.label }}</div>
                </a-col>
            </a-row>
        </a-collapse-panel>
        <a-collapse-panel key="2" header="自定义字段" :style="customStyle">
             <a-row :gutter="16">
                <a-col class="gutter-row" :span="12" v-for="(item,index) in customList" :key="index">
                    <div class="gutter-box" draggable="true" @dragstart="dragStart($event,item)"
                    @dragover="dragOver"
                    @drop="drop">{{ item.label }}</div>
                </a-col>
            </a-row>
        </a-collapse-panel>
    </a-collapse>
    </div>
</template>

<script setup>
import { CaretRightOutlined } from '@ant-design/icons-vue';
import { onMounted,reactive } from 'vue';
import {fieldInfo} from "./core"
import {deobfuscate} from '@/utils/common.js';
    const {systemList,customList,flowList , docReceiptsList,memoDraftsList} = fieldInfo

    const customStyle =  'background: #fff;border-radius: 4px;margin-bottom: 0;border: 0;overflow: hidden';

    const route = useRoute()
    const state = reactive({
        activeKey:"1",
        list:[]
    })

    function dragStart(e,item) {
        console.log(e,item)
         // 设置传输数据
        e.dataTransfer.setData("application/json", JSON.stringify(item));
        e.dataTransfer.setDragImage(e.target, 10, 10);
        
    }

    function dragOver(e) {
        e.preventDefault();
    }

    onMounted(()=>{
        const query = deobfuscate(route.query.code)
        const type  = query.type

        const mapList = {
            docDrafts: systemList,
            docReceipts:docReceiptsList,
            memoDrafts:memoDraftsList
        }
        state.list = mapList[type]        

        console.log('type',type)
    })
   
</script>

<style lang="less" scoped>
    .field-list{
        position: relative;
        width: 250px;
        background-color: #fff;
        padding-top: 10px;

        .gutter-box{
            border: 1px solid #d9d9d9;
            font-size: 14px;
            height: 32px;
            padding: 4px 15px;
            border-radius: 4px;
            cursor: move;
            margin-top: 16px;
            user-select: none;
            overflow: hidden;
            &:hover{
                // border-style:dashed;
                border-color: #1fc48d;
                color: #1fc48d;
            }
        }
    }
</style>
