<template>
<div class="manuscript-design">
    <FieldList/>
   <div class="online-excel">
    <div class="draw-area">
      <div class="draw-area-2">
        <!--  -->
        <div class="toolbar">
          <Toolbar :style="activeSelectStyle" @change="changeCellStyle" />
        </div>
        <!--  -->
        <div class="excel-content">
          <div class="excel-body">
            <div class="main-box designer-layout">
              <div class="content-layer">
                <div class="col-header">
                  <div class="cols-header-scroller">
                    <!-- col header -->
                    <div class="cols-header__item" :class="{
                      'header-selected': isSelecting('col', colIndex),
                    }" style="width: 80px; min-width: 50px" v-for="(col, colIndex) in excel.columns" :key="colIndex"
                      @click.stop="onColItem(col, colIndex)" :style="{ width: `${col.width}px` }">
                      <span class="cols-header__text">{{ col.value }}</span>
                      <span class="cols-header__selector" :class="{ active: isActiveSize('col', col, colIndex) }"
                        :style="state.updateWStyle" @mousedown.stop="
                          (e) => stareUpdateWidth(e, col, colIndex)
                        "></span>
                    </div>
                  </div>
                </div>
                <div class="row-header">
                  <div class="rows-header-scroller" style="height: calc(100vh - 104px)"
                    @scroll="(e) => scrollHandler(e, 'row')">
                    <!-- row header -->
                    <div class="rows-header__item" :class="{
                      'header-selected': isSelecting('row', rowIndex),
                    }" v-for="(row, rowIndex) in excel.data" :key="rowIndex" @click.stop="onRowItem(row, rowIndex)"
                      :style="{ height: `${row.height}px` }">
                      <div class="rows-header__text">
                        {{ rowIndex }}
                      </div>
                      <div class="rows-header__selector" :class="{ active: isActiveSize('row', row, rowIndex) }"
                        :style="state.updateHStyle" @mousedown.stop="
                          (e) => stareUpdateHeight(e, row, rowIndex)
                        "></div>
                    </div>
                  </div>
                </div>
                <div class="corner-header"></div>

                <div class="table-body-wrap">
                  <div class="table-body" style="height: calc(100vh - 106px); width: 100vw"
                    @scroll="(e) => scrollHandler(e, 'rowBody')">
                    <!--  -->
                    <a-dropdown  :trigger="['contextmenu']">
                      <!-- 操作区域 -->
                      <table class="content-table" tabindex="0">
                        <thead>
                          <tr>                           
                            <th :style="{
                              width: `${col.width}px`,
                              maxWidth: `${col.width}px`,
                              minWidth: `${col.width}px`,
                            }" :data-col="colIndex + 1" v-for="(col, colIndex) in excel.columns" :key="colIndex">
                            </th>
                          </tr>
                        </thead>
                        <!--  -->
                        <tbody>
                          <tr class="row" :data-row="rowIndex + 1" v-for="(row, rowIndex) in excel.data" :key="rowIndex"
                            :style="{ height: `${row.height}px` }">
                            <td class="cell" :data-col="col.colIndex" v-for="(col, colIndex) in row"
                              :rowspan="col.rowspan" :colspan="col.colspan" :class="col.className" :key="colIndex"
                              @mousedown="startSelection($event, col)" @mouseover="extendSelection(col)"
                              @dblclick.stop="dblclickItem(col)">
                              <!-- content -->
                              <div class="wrap" >
                                <div class="cell-data">
                                  <textarea v-autofocus v-if="isActiveCell(col)" v-model="col.value" wrap="soft"
                                    class="content content-edit"></textarea>
                                  <div class="content"  v-else :style="col.style" @dragenter="(e)=>dragenter(e,col)" @drop="(e)=>drop(e,col)" @dragover="allowDrop">
                                    {{ col.value }}
                                  </div>
                                </div>
                              </div>
                              <!--  -->
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- 右键菜单 -->
                      <template #overlay>
                        <a-menu @click="handleContextMenuClick">
                          <a-menu-item key="shear">剪切</a-menu-item>
                          <a-menu-item key="cope">复制</a-menu-item>
                          <a-menu-item key="paste">粘贴</a-menu-item>
                          <a-menu-item key="clear" type="divider">
                            清空选中区域内容
                          </a-menu-item>
                          <a-divider type="horizontal" style="margin: 3px 0" />
                          <a-menu-item key="height">
                            <span>行高</span>
                              <a-input-number v-model:value="state.rowHeight" :min="1" :max="100" type="number" @click.stop style="width: 65px; margin: 0 5px 0 20px" size="small">
                              </a-input-number>
                            <span>mm</span>
                          </a-menu-item>
                          <a-menu-item key="width">
                            <span>列宽</span>
                              <a-input-number v-model:value="state.colWidth" :min="1" :max="100" type="number" @click.stop style="width: 65px; margin: 0 5px 0 20px" size="small">                               
                              </a-input-number>
                            <span style="margin-left: 5px">mm</span>
                          </a-menu-item>
                          <a-divider type="horizontal" style="margin: 3px 0" />
                          <a-menu-item :key="state.isSplitMerge ? 'split' : 'merge'">{{
                            state.isSplitMerge ? "拆分" : "合并"
                            }}单元格</a-menu-item>
                        </a-menu>
                      </template>
                      <!--  -->
                    </a-dropdown>
                    <!--  -->
                    <!-- border -->
                    <table class="border-table">
                      <thead>
                        <tr>
                          <th :style="{
                            width: `${col.width}px`,
                            maxWidth: `${col.width}px`,
                            minWidth: `${col.width}px`,
                          }" :data-col="colIndex + 1" v-for="(col, colIndex) in excel.columns" :key="colIndex"></th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr class="row" :data-row="rowIndex + 1" v-for="(row, rowIndex) in excel.data" :key="rowIndex"
                          :style="{ height: `${row.height}px` }">
                          <td class="cell" :data-col="col.colIndex" v-for="(col, colIndex) in row"
                            :class="`${col.className} ${col.classNameBorder}`" :rowspan="col.rowspan" :colspan="col.colspan" :style="col.styleBorder" :key="colIndex">
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <div class="background-layer">
                      <div class="table-dashed-line" :style="{
                        width: `${print.width}mm`,
                        height: `${print.height}mm`
                      }"></div>
                      <div class="table-unprintable-hint" :style="`left: ${print.width}mm`">
                        灰色区域不可打印
                      </div>
                    </div>
                    <div class="interact-layer">
                      <div class="cell-selected-wrap">
                        <div class="selected-mask" :style="state.selectedStyle"></div>
                      </div>
                      <div class="cell-clip-wrap" v-if="state.shears.length > 0">
                        <svg data-v-c934ef84="" xmlns="http://www.w3.org/2000/svg" class="clip-mask"
                          :style="state.shearStyle">
                          <rect data-v-c934ef84="" :width="state.shearStyle.width" :height="state.shearStyle.height">
                            <animate data-v-c934ef84="" attributeName="stroke-dashoffset" dur="3s"
                              repeatCount="indefinite" keyTimes="0;1" values="0;-200"></animate>
                          </rect>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="excel-sheet">
        <div class="sheet-item active">
          <div class="sheet-page">
            <span class="sheet-title">sheet1</span>
          </div>
        </div>
      </div>
    </div>
   </div>
   <!--  -->
   <Setting v-model:value="state.settingFieldData"/>
</div>
</template>

<script setup>
import { onMounted, reactive, watch, nextTick, ref,toRaw } from "vue";
import Excel from "./core";
import Toolbar from "./toolbar.vue";
import FieldList from "./fieldList.vue";
import Setting from "./setting.vue";
// TODO: 待优化
// 宽度 减少 计算位置限制错误
// 高度 减少 计算位置限制错误

const excel = reactive(new Excel());
console.log(excel)
// 点击非自身元素指令
const vClickOutside = {
  beforeMount(el, binding) {
    el.clickOutsideEvent = function (event, params) {
      if (!(el === event.target || el.contains(event.target))) {
        binding?.value(params);
      }
    };
    document.addEventListener("click", el.clickOutsideEvent);
  },
  unmounted(el) {
    document.removeEventListener("click", el.clickOutsideEvent);
  },
};

const props = defineProps({
  printDirection:{
    type:String,
    default:"vertical" // 纵向 vertical 横向 horizontal
  },
  printOptions:{
    type:Object,
    default:()=>{
      return {
        width: 210,
        height: 297,
        top: 20,
        bottom:20,
        left: 17,
        right:17,
      }
    }
  },
  options:{
    type:Object,
    default:()=>{
      return {
        merges:[],
        cells:{},
        cols:{},
        rows:{}
      }
    }
  }
})

const state = reactive({
  selectedStyle: {},
  merges: [], // 合并单元格信息 { start: {row, col}, end: {row, col} }
  // 是否选择中
  isDragging: false,
  // 是否更新宽高
  isUpdateWH: false,
  // 正在更新宽度高度对象
  updateSize: null,

  selectionStart: null,
  selectedCells: [],

  updateWStyle: {},
  updateHStyle: {},

  // 是否拆分单元格
  isSplitMerge: false,

  // 激活单元格 编辑模式 // A1
  activeCell: "",

  // 剪切复制 单元格
  shears: [],
  shearStyle: {},  
  beforePaste:"", // 粘贴前的操作
  // 
  rowHeight:7.408333333333333,
  colWidth: 21.166666666666668,
  // 变量字段设置
  settingFieldData:{}
});

const print = computed(()=>{
  if(props.printDirection == "vertical"){
    return {
      width: props.printOptions.width - props.printOptions.left - props.printOptions.right,
      height: props.printOptions.height - props.printOptions.top - props.printOptions.bottom,
    }
  }
  if(props.printDirection == "horizontal"){
    return {
      width: props.printOptions.height - props.printOptions.left - props.printOptions.right,
      height: props.printOptions.width - props.printOptions.top - props.printOptions.bottom,
    }
  }
})

const activeSelectStyle = computed(()=>{
  const item = state.selectedCells[0] || {}
  // 选中一个单元格, 或者选中的是合并单元格，激活回显样式
  if(state.selectedCells.length == 1 || state.selectedCells.some(cell=> isInAnyMergedCell(cell))){
    const data = state.selectedCells.map(i => {      
      return excel.getExcelColumnLetters(i.col+1) + (i.row + 1)
    }) 
    let idx = 0,style = {}
    for (let i = 0; i < excel.data.length; i++) {
      if (data.length == idx) {
        break;
      }
      const row = excel.data[i]
      for (let k in row) {
        const col = row[k]
        if (data.includes(col.col)) {
          style = col.style
          idx++
        }
        continue;
      }
    }    
    const {fontSize,fontFamily,verticalAlign,textAlign,textDecoration,fontStyle,fontWeight,borderColor,backgroundColor,color} = style
    return {
      fontSize,
      fontFamily,
      verticalAlign,
      textAlign,
      textDecoration,
      fontStyle,
      fontWeight,
      color,
      backgroundColor,
      borderColor
    }
  }else{
    return {}
  }
})

const isSelecting = (type, index) => {
  if (state.selectedCells.length === 0) return false;
  if (type === "col") {
    return state.selectedCells.some((cell) => cell.col === index);
  } else if (type === "row") {
    return state.selectedCells.some((cell) => cell.row === index);
  }
  return false;
};

// 是否激活 修改宽度高度
const isActiveSize = (type, col, colIndex) => {
  if (
    state.updateSize &&
    state.updateSize.colIndex === colIndex + 1 &&
    type === "col"
  ) {
    return true;
  }
  if (
    state.updateSize &&
    state.updateSize.rowIndex === colIndex &&
    type === "row"
  ) {
    return true;
  }
  return false;
};

// 是否激活单元格
const isActiveCell = (col) => {
  return state.activeCell == col.col;
};

watch(()=> props.options,()=>{
  initExcel()
},{
  deep:true
})

// 初始化excel
function initExcel(){
  const { merges=[],cells={},cols={},rows={} } = props.options
  state.merges = [...merges]
  
  for(let len in cols){
    const width = cols[len]
    if(len == 'length'){
      continue
    }else{
      excel.columns[len-1].width = width      
    }
  }
  for(let len in rows){
    const height = rows[len]
    if(len == 'length'){
      continue
    }else{
      excel.data[len-1].height = height
    }
  }
  let index = 0
  for(let key in cells){
    if(Object.keys(cells).length == index){
      break
    }
    const cell = cells[key]
    for(let i in excel.data){
      if(Object.keys(cells).length == index){
        break
      }
      const row = excel.data[i]
      for(let k in row){
        const col = row[k]
        if(col.col == key){
            // 
            for(let j in cell){
              col[j] = cell[j]
            }
            index++
        }
        continue;
      }
    }
  }
}

// 获取 编辑数据
function getContent(){
  const content = {
    merges: toRaw(state.merges),
    cells:{},
    cols:{
      length:excel.columns.length
    },
    rows:{
      length:excel.data.length,
    }
  }

  excel.columns.forEach((col,index)=>{
    if(col.width != 80){
      content.cols[index+1] = col.width
    }
  })

  excel.data.forEach((row,index)=>{   
    if(row.height != 28){
      content.rows[index+1] = row.height
    }
    for(let i in row){
      const col = row[i]
      if(Object.keys(col.fieldInfo).length || Object.keys(col.style).length || Object.keys(col.styleBorder).length || col.className || col.classNameBorder || col.value || (col.colspan!=null && col.rowspan!=null)){
        content.cells[col.col] = {}
      }
      if(Object.keys(col.style).length){
        content.cells[col.col]['style'] = col.style
      }
      if(Object.keys(col.styleBorder).length){
        content.cells[col.col]['styleBorder'] = col.styleBorder
      }
      if(col.className){
        content.cells[col.col]['className'] = col.className
      }
      if(col.classNameBorder){
        content.cells[col.col]['classNameBorder'] = col.classNameBorder
      }
      if(col.value){
        content.cells[col.col]['value'] = col.value
      }
      if(col.colspan!=null && col.rowspan!=null){
        content.cells[col.col]['colspan'] = col.colspan
        content.cells[col.col]['rowspan'] = col.rowspan
      }
      if(Object.keys(col.fieldInfo).length){
        content.cells[col.col]['fieldInfo'] = col.fieldInfo
      }

    }
  })
  console.log('__content___',content)
  return toRaw(content)
}

// 拖动元素进入 
function dragenter(e,col){
  console.log("拖动元素进入",col.col)
  e.dataTransfer.dropEffect = 'copy';
  const cell = { row: col.rowIndex - 1, col: col.colIndex - 1}
  if(isMergeCell(cell.row,cell.col)){
    const mergeCell = state.merges.find(
      (i) => i.start.row == cell.row && i.start.col == cell.col
    );    
    if (mergeCell) {
      const selectedCells = [];
      for (let r = mergeCell.start.row; r <= mergeCell.end.row; r++) {
        for (let c = mergeCell.start.col; c <= mergeCell.end.col; c++) {         
          selectedCells.push({ row: r, col: c });
        }
      }
      state.selectedCells = selectedCells;
    }
    return
  }else{
    state.selectedCells = [cell];
  }
}
// 拖动元素放下
function drop(e,col){
  e.preventDefault();
  // console.log("拖动元素放下",col.col,col)
  const jsonString = e.dataTransfer.getData("application/json");
  if (jsonString) {
    // 解析回对象
    const field = JSON.parse(jsonString);    
    console.log("拖动元素放下:", col,field);
    col.value = field.label
    //计算下一个value区域
    
    const cell = { row: col.rowIndex - 1, col: col.colIndex - 1}
    // TODO: xxxx
    const nextCell = {row:col.rowIndex,col:col.colIndex + (isMergeCell(cell.row,cell.col) ? col.colspan : 1)}
    const nextCol = excel.data[col.rowIndex -1 ]
    for(let i in nextCol){
      const ncol = nextCol[i]
      if(ncol.rowIndex == nextCell.row && ncol.colIndex == nextCell.col){
        ncol.value = field.value
        ncol.className = isMergeCell(cell.row,cell.col) ? ncol.className + ' field-content' : "field-content"
        ncol.fieldInfo = field
        break;
      }
      continue
    }
  }
}

function allowDrop(e){
  e.dataTransfer.dropEffect = 'copy';
  e.preventDefault();
}


// 给指定单元格加边框
function addCellBorder(data, className, isRemove = false, width = '', color) {
  console.log(data)
  let idx = 0
  for (let i = 0; i < excel.data.length; i++) {
    if (data.length == idx) {
      // 选中数据处理完成, 直接跳出循环
      break;
    }
    const row = excel.data[i]
    for (let k in row) {
      const col = row[k]
      if (data.includes(col.col)) {
        if (isRemove) {
          col.styleBorder = {}
          col.classNameBorder = ""
        } else {
          if (width) {
            const arr = col.classNameBorder?.split(" ")
            const idx = arr.findIndex(i => i.indexOf('width') > -1)
            if (idx > 0) {
              arr.splice(idx, 1)
            }
            const names = [...new Set([...arr, className, width])].join(" ")
            // console.log("______",names)
            col.classNameBorder = isRemove ? '' : names
          } else {
            col.classNameBorder = isRemove ? '' : col.classNameBorder            
            col.styleBorder[`${className}-color`] = `rgb(0, 0, 0) !important`

            if(color){
              // col.styleBorder[`${className}-color`] = `${color} !important`
              console.log(col.styleBorder,col.col)
            }
          }
        }
        idx++
        continue
      } else {
        continue
      }
    }
  }
}

// 给单元格加样式
function addCellStyle({ key, value }) {
  console.log(key,value)
  const cell = state.selectedCells.map(i => {
    return excel.getExcelColumnLetters(i.col + 1) + (i.row + 1)
  })
  let idx = 0
  for (let i = 0; i < excel.data.length; i++) {
    if (cell.length == idx) {
      // 选中数据处理完成, 直接跳出循环
      break;
    }
    const row = excel.data[i]
    for (let k in row) {
      const col = row[k]
      if (cell.includes(col.col)) {
        if(key=='fontWeight'){
          col.style[key] = col.style[key]=='bold'? 'normal': value
          continue
        }
        if(key=='fontStyle'){
          col.style[key] = col.style[key]=='italic'? 'normal': value
          continue
        }
        if(key=='textDecoration'){
          col.style[key] = col.style[key]=='underline'? 'none': value
          continue
        }
        col.style[key] = value
      }
      continue
    }
  }
}

// 样式处理
function changeCellStyle({ key, value }) {
  // 由于边框样式是在单元格的右下，因此要往前推一组
  switch (key) {
    case "border-left":
      // 找到选择区域 左边的一组数据
      const leftCell = getSideCell(state.selectedCells, 'left')
      addCellBorder(leftCell, 'border-right')
      break;
    case "border-right":
      // 找到选择区域 右边的一组数据
      const rightCell = getSideCell(state.selectedCells, 'right')
      addCellBorder(rightCell, 'border-right')
      break;
    case "border-top":
      // 找到选择区域 右边的一组数据
      const topCell = getSideCell(state.selectedCells, 'top')
      addCellBorder(topCell, 'border-bottom')
      break;
    case "border-bottom":
      const bottomCell = getSideCell(state.selectedCells, 'bottom')
      addCellBorder(bottomCell, 'border-bottom')
      break;
    case "border":
      // 外边框
      const { left, right, top, bottom } = getSideCell(state.selectedCells, 'border')
      addCellBorder(top, 'border-bottom')
      addCellBorder(left, 'border-right')
      addCellBorder(right, 'border-right')
      addCellBorder(bottom, 'border-bottom')
      break
    case "whole":
      // 全边框      
      const wholeCell = getSideCell(state.selectedCells, 'whole')
      addCellBorder(wholeCell.top, 'border-bottom')
      addCellBorder(wholeCell.left, 'border-right')
      addCellBorder(wholeCell.right, 'border-right')
      addCellBorder(wholeCell.bottom, 'border-bottom')
      addCellBorder(wholeCell.inside, 'border-right')
      break
    case "noBorder":
      const noneCell = getSideCell(state.selectedCells, 'whole')
      addCellBorder(noneCell.top, 'border-bottom', true)
      addCellBorder(noneCell.left, 'border-right', true)
      addCellBorder(noneCell.right, 'border-right', true)
      addCellBorder(noneCell.bottom, 'border-bottom', true)
      addCellBorder(noneCell.inside, 'border-right', true)
      console.log(noneCell)
      break
    case "border-width-small":
    case "border-width-middle":
    case "border-width-large":
      const widthCell = getSideCell(state.selectedCells, 'whole')
      addCellBorder(widthCell.top, 'border-bottom', false, key)
      addCellBorder(widthCell.left, 'border-right', false, key)
      addCellBorder(widthCell.right, 'border-right', false, key)
      addCellBorder(widthCell.bottom, 'border-bottom', false, key)
      addCellBorder(widthCell.inside, 'border-right', false, key)
      break
    case "border-color":
      const colorCell = getSideCell(state.selectedCells, 'whole')
      // 检查选择区域哪些是已经加了色的，在修改成当前色
      const arr = [...new Set(Object.values(colorCell).reduce((list,item)=>{
        return list.concat(item)
      },[]))]
      let idx = 0
      for (let i = 0; i < excel.data.length; i++) {
        if (arr.length == idx) {
          break;
        }
        const row = excel.data[i]
        for (let k in row) {
          const col = row[k]
          if (arr.includes(col.col)) {
            Object.keys(col.styleBorder).forEach(key=>{
              col.styleBorder[key] = value
            })
            idx++
          }
          continue
        }
      }
      break
    default:
      addCellStyle({
        key,
        value
      })
      break;
  }



}

// TODO: 计算选中区域应该排除合并，并且border 层不应该合并计算

// 获取选择区域侧边 单元格
function getSideCell(data, direction) {
  if (direction == 'left') {   
    return findMinItemsPerRow(data, direction).map(i => {      
      return excel.getExcelColumnLetters(i.col) + (i.row + 1)
    })
  }
  if (direction == 'right') {
    if((data.every(cell=>isInAnyMergedCell(cell)))){
      return findMinItemsPerRow([data[0]], direction).map(i => {
        return excel.getExcelColumnLetters(i.col + 1) + (i.row + 1)
      })
    }
    return findMinItemsPerRow(data, direction).map(i => {
      return excel.getExcelColumnLetters(i.col + 1) + (i.row + 1)
    })
  }
  if (direction == 'top') {   
    return findMinItemsPerRow(data, direction).map(i => {
      return excel.getExcelColumnLetters(i.col + 1) + i.row
    })
  }
  if (direction == 'bottom') {
    if((data.every(cell=>isInAnyMergedCell(cell)))){
      return findMinItemsPerRow([data[0]], direction).map(i => {
        return excel.getExcelColumnLetters(i.col + 1) + (i.row + 1)
      })
    }
    return findMinItemsPerRow(data, direction).map(i => {
      return excel.getExcelColumnLetters(i.col + 1) + (i.row + 1)
    })
  }
  // 外边框
  if (direction == 'border') {
    const borderCell = findMinItemsPerRow(data, 'border')
    const top = getSideCell(borderCell, 'top')
    const right = getSideCell(borderCell, 'right')
    const bottom = getSideCell(borderCell, 'bottom')
    const left = getSideCell(borderCell, 'left')
    return {
      top,
      left,
      right,
      bottom
    }
  }
  // 全边框
  if (direction == 'whole') {
    let { top, left, right, bottom } = getSideCell(data, 'border')
    const maxRow = Math.max(...data.map(item => item.row));
    const maxCol = Math.max(...data.map(item => item.col));
    // 右边
    const list = data.filter(item => item.row <= maxRow && item.col <= maxCol).map(i => {
      return excel.getExcelColumnLetters(i.col + 1) + (i.row + 1)    
    })
    // 找到里面存在和合并单元格
    // const mergedCells = getMergedCellsInSelection(data);
    // const allMerge = mergedCells.map(item=>{
    //   const selectedCells = [];
    //   for (let r = item.start.row; r <= item.end.row; r++) {
    //     for (let c = item.start.col; c <= item.end.col; c++) {
    //       // debugger
    //       selectedCells.push({ row: r, col: c });
    //     }
    //   }
    //   return selectedCells
    // })
    // const arr = data.filter(cell=>{
    //   if(isInAnyMergedCell(cell)){
    //     return false
    //   }
    //   return true
    // })
    // console.log('-----',arr,allMerge)

    top = [...top, ...list]
    return {
      top,
      left,
      right,
      bottom,
      inside:list
    }
  }

}

// 1.找到选择区域 指定方向的一组数据
// 2.上下 找到最大最小的col  左右 找到最大最小的row
function findMinItemsPerRow(data, direction) {
  const groups = {};
  // 按 row 分组
  data.forEach(item => {
    if (!groups[item.row]) {
      groups[item.row] = [];
    }
    groups[item.row].push(item);
  });

  if (direction == 'left') {
    // 计算每行的最小 col
    return Object.values(groups).map(group =>
      group.reduce((min, item) => (item.col < min.col ? item : min))
    );
  }

  if (direction == 'right') {
    // 计算每行的最大 col
    return Object.values(groups).map(group =>
      group.reduce((max, item) => (item.col > max.col ? item : max))
    );
  }

  if (direction == 'top') {
    // 计算每行的最小 row
    const minRow = Math.min(...data.map(item => item.row));
    return data.filter(item => item.row === minRow);
  }

  if (direction == 'bottom') {
    // 计算每行的最大 row
    const minRow = Math.max(...data.map(item => item.row));
    return data.filter(item => item.row === minRow);
  }

  // 外边框 最外一圈
  if (direction == 'border') {
    // 1. 计算 minRow, maxRow, minCol, maxCol
    const rows = data.map(item => item.row);
    const cols = data.map(item => item.col);
    const minRow = Math.min(...rows);
    const maxRow = Math.max(...rows);
    const minCol = Math.min(...cols);
    const maxCol = Math.max(...cols);

    // 2. 筛选最外圈的格子
    return data.filter(item =>
      item.row === minRow ||
      item.row === maxRow ||
      item.col === minCol ||
      item.col === maxCol
    );
  }
}

// 右键菜单处理
function handleContextMenuClick(e) {
  console.log(e);
  switch (e.key) {
    case "merge":
      mergeCells();
      break;
    case "split":
      splitCell();
      break;
    case "shear":
      state.beforePaste = "shear"
      shearCells();
      break;
    case "cope":     
      state.beforePaste = "cope" 
      copeCells();
      break;
    case "paste":      
      pasteCells();
      break;
    case "clear":
      clearCells();
      break;
    case  "height":
      updateHeightCells();
      break
    case  "width":
      updateWidthCells();
      break
    default:
      break;
  }
}
// 修改选择区域的高度
function updateHeightCells(){
  const rowList = [...new Set(state.selectedCells.map(item=>item.row))]
  rowList.forEach(idx=>{
    excel.data[idx].height = state.rowHeight /  0.264583
  })
  // 更新选择定位高度
  updatePosition()
}

// 修改选择区域的宽度
function updateWidthCells(){
  const colList = [...new Set(state.selectedCells.map(item=>item.col))]
  colList.forEach(idx=>{
    excel.columns[idx].width = state.colWidth /  0.264583
  })
  // 更新row 里面 的col 宽度 (待优化，可以不用里面设置)
  excel.data.forEach(row=>{
    const columns = colList.map(i=>row.getKeyByIndex(i+1))
    columns.forEach(col=>{
      row[col].width = state.colWidth /  0.264583
    })
  })
  // 更新选择定位高度
  updatePosition()
}

// 清除单元格内容
function clearCells(){
  const data = state.selectedCells.map(i => {
    return excel.getExcelColumnLetters(i.col+1) + (i.row + 1)
  }) 
  let idx = 0
  for (let i = 0; i < excel.data.length; i++) {
    if (data.length == idx) {
      break;
    }
    const row = excel.data[i]
    for (let k in row) {
      const col = row[k]
      if (data.includes(col.col)) {
        col.value = ''
        col.fieldInfo = {}
        if(col.className.indexOf('field-content') != -1){
          col.className = col.className.replace('field-content','')
        }
        idx++
      }
      continue;
    }
  }
}

// 剪切
function shearCells() {
  state.shears = state.selectedCells.map(i => {
    return { ...i }
  })
  state.shearStyle = calculateDivPosition(state.shears);
  console.log('剪切', state.shears)
}
// 复制
function copeCells(){
  state.shears = state.selectedCells.map(i => {
    return { ...i }
  })
  state.shearStyle = calculateDivPosition(state.shears);
}
// 粘贴
function pasteCells() {
  // todo
  // state.shears = []
  // state.shearStyle = {}

  console.log(state.beforePaste)
  console.log(state.shears)
  console.log(state.selectedCells)

}

//  合并单元格
function mergeCells() {
  const mergedList = getMergedCellsInSelection(state.selectedCells);
  if (mergedList.length > 0) {
    console.log(`选择的区域 包含 ${mergedList.length} 已经合并的`, mergedList);

    mergedList.forEach((item) => {
      const index = state.merges.findIndex(
        (merge) =>
          merge.start.row === item.start.row &&
          merge.start.col === item.start.col
      );
      if (index != -1) {
        state.merges.splice(index, 1);
      }
    });
  }
  const [rows, cols] = [[], []];
  state.selectedCells.forEach((cell) => {
    rows.push(cell.row);
    cols.push(cell.col);
  });
  const minRow = Math.min(...rows);
  const maxRow = Math.max(...rows);
  const minCol = Math.min(...cols);
  const maxCol = Math.max(...cols);
  const awaitCell = {
    start: { row: minRow, col: minCol },
    end: { row: maxRow, col: maxCol },
  };
  console.log("待合并的单元格", awaitCell);
  const merge = { rowspan: maxRow - minRow + 1, colspan: maxCol - minCol + 1 };
  state.selectedCells.forEach((cell, index) => {
    if (index == 0) {
      const row = excel.data[cell.row];
      const key = row.getKeyByIndex(cell.col + 1);
      const firstCol = row[key];
      firstCol.rowspan = merge.rowspan;
      firstCol.colspan = merge.colspan;
    } else {
      const row = excel.data[cell.row];
      const key = row.getKeyByIndex(cell.col + 1);
      const otherCell = row[key];
      otherCell.className = "hidden";
      // console.log(row[key])
    }
  });

  state.merges.push(awaitCell);
  console.log("已经合并后的单元格", state.merges);
}

// 拆分单元格
function splitCell() {
  const mergeCell = state.merges.find(
    (merge) =>
      merge.start.row === state.selectedCells[0].row &&
      merge.start.col === state.selectedCells[0].col
  );
  if (mergeCell) {
    const selectedCells = getMergeAllCell(mergeCell);
    selectedCells.forEach((cell) => {
      const row = excel.data[cell.row];
      const key = row.getKeyByIndex(cell.col + 1);
      const otherCell = row[key];
      otherCell.rowspan = null;
      otherCell.colspan = null;
      otherCell.className = ''
    });
    state.merges.splice(state.merges.indexOf(mergeCell), 1);
  }
  console.log(state.merges)
}

// 双击单元格
function dblclickItem(e) {  
  // 存在变量字段不能修改
  if(Object.keys(e.fieldInfo).length > 0){
    return
  }
  state.activeCell = e.col
  console.log("激活单元格", state.activeCell)
}

function onColItem(col, colIndex) {
  if (state.isUpdateWH) return;

  console.log("col", col, colIndex, state.isUpdateWH);
  state.selectedCells = excel.data.map(
    ({ height, width, value }, rowIndex) => ({
      row: rowIndex,
      col: colIndex,
      height,
      width,
      value,
    })
  );
}

function onRowItem(row, rowIndex) {
  if (state.isUpdateWH) return;
  console.log("row", row, rowIndex);
  state.selectedCells = excel.columns.map(
    ({ height, width, value }, colIndex) => ({
      row: rowIndex,
      col: colIndex,
      height,
      width,
      value,
    })
  );
}

// 是否合并的单元格
function isMergeCell(row, col) {
  for (const merge of state.merges) {
    if (
      row >= merge.start.row &&
      row <= merge.end.row &&
      col >= merge.start.col &&
      col <= merge.end.col
    ) {
      return true;
    }
  }
  return false;
}

// 获取合并单元格的所有格子
function getMergeAllCell(mergeCell) {
  const selectedCells = [];
  for (let r = mergeCell.start.row; r <= mergeCell.end.row; r++) {
    for (let c = mergeCell.start.col; c <= mergeCell.end.col; c++) {
      selectedCells.push({ row: r, col: c });
    }
  }
  return selectedCells;
}

// 开始选择
function startSelection(e, item) {
  const { rowIndex, colIndex, height, width, value, rowspan, colspan } = item;
  // 当前 激活的单元格 不是清楚，是不能选区
  if (state.activeCell != item.col) {
    state.activeCell = ''
  } else {
    return
  }
  // 右键菜单
  if (e?.button === 2) {
    // 如果已经选择大于1格单元格,  并且右键的单元格是已经选择的单元格, 右键不再重新选择
    if(state.selectedCells.length > 1 && state.selectedCells.find((i) => i.row == rowIndex - 1 && i.col == colIndex - 1)){
        if (state.selectedCells.every(cell=>isInAnyMergedCell(cell))) {
          console.log("拆分单元格");
          state.isSplitMerge = true;
        }
        // 右键单元格 w h 设为默认值, 换算成mm
        state.colWidth  = excel.width * 0.264583
        state.rowHeight = excel.height * 0.264583
        return;
    }
  }

  state.isSplitMerge = false;
  state.selectionStart = {
    row: rowIndex - 1,
    col: colIndex - 1,
    height,
    width,
    value,
  };

  // 是否合并的单元格
  if (rowspan != null && colspan != null) {
    // state.selectedCells = [{ row: rowspan-1, col: colspan-1}];
    // 找到选中的边界
    const mergeCell = state.merges.find(
      (i) => i.start.row == rowIndex - 1 && i.start.col == colIndex - 1
    );
    // console.log(
    //   "是否合并的单元格",
    //   isMergeCell(rowIndex - 1, colIndex - 1),
    //   mergeCell
    // );
    if (mergeCell) {
      const selectedCells = [];
      for (let r = mergeCell.start.row; r <= mergeCell.end.row; r++) {
        for (let c = mergeCell.start.col; c <= mergeCell.end.col; c++) {
          // debugger
          selectedCells.push({ row: r, col: c });
        }
      }
      // 选取 合并选中
      state.selectedCells = selectedCells;
      state.settingFieldData = item.fieldInfo
    }
  } else {
    state.selectedCells = [
      { row: rowIndex - 1, col: colIndex - 1, height, width, value, cell: item.col },
    ];
  }

  state.isDragging = true;
  console.log("点击选中的单元格", state.selectedCells,item);
  // 只有一个单元格
  if(state.selectedCells.length == 1){
    // 点击变量字段
    if(Object.keys(item.fieldInfo).length > 0 && e?.button == 0){
      // console.log("fieldInfo",item.fieldInfo)
      state.settingFieldData = item.fieldInfo
      return
    }
    state.settingFieldData = {}
    // 鼠标右健 获取单元格的 width height
    if(e?.button == 2){
      state.rowHeight = excel.data[rowIndex-1].height * 0.264583
      state.colWidth = excel.columns[colIndex-1].width * 0.264583
    }
  }  
}

// 单元格 是否在 合并单元格内
function isInAnyMergedCell(cell) {
  return state.merges.some(
    (merged) =>
      cell.row >= merged.start.row &&
      cell.row <= merged.end.row &&
      cell.col >= merged.start.col &&
      cell.col <= merged.end.col
  );
}
// 判断选择区域是否包含合并单元格的格子
function doesSelectionContainMergedCells(selectedCells) {
  // 将选择的单元格转换为Set以便快速查找
  const selectedSet = new Set(selectedCells.map((c) => `${c.row},${c.col}`));

  // 检查每个合并区域是否与选择区域有交集
  return state.merges.some((merged) => {
    for (let r = merged.start.row; r <= merged.end.row; r++) {
      for (let c = merged.start.col; c <= merged.end.col; c++) {
        // 找到交集
        if (selectedSet.has(`${r},${c}`)) {
          return true;
        }
      }
    }
    return false;
  });
}

// 获取选择区域包含的所有合并区域
function getMergedCellsInSelection(selectedCells) {
  const selectedSet = new Set(selectedCells.map((c) => `${c.row},${c.col}`));
  return state.merges.filter((merged) => {
    for (let r = merged.start.row; r <= merged.end.row; r++) {
      for (let c = merged.start.col; c <= merged.end.col; c++) {
        if (selectedSet.has(`${r},${c}`)) {
          return true;
        }
      }
    }
    return false;
  });
}

function extendSelection(e) {
  if (!state.isDragging || !state.selectionStart) return;
  if (state.isUpdateWH) return;
  const row = e.rowIndex - 1;
  const col = e.colIndex - 1;
  console.log(e.col);
  const startRow = Math.min(state.selectionStart.row, row);
  const endRow = Math.max(state.selectionStart.row, row);
  const startCol = Math.min(state.selectionStart.col, col);
  const endCol = Math.max(state.selectionStart.col, col);

  let newMinRow = startRow;
  let newMaxRow = endRow;
  let newMinCol = startCol;
  let newMaxCol = endCol;

  const selected = [];
  for (let r = newMinRow; r <= newMaxRow; r++) {
    for (let c = newMinCol; c <= newMaxCol; c++) {
      selected.push({ row: r, col: c });
    }
  }

  const result = calculateExpandedSelection(selected, state.merges);
  console.log("扩展后的选择区域:", result.expandedSelection);

  state.selectedCells = [...result.expandedSelection];
}

function calculateExpandedSelection(selectedPoints, mergedCells) {
  // 如果选择区域为空，直接返回空数组
  if (selectedPoints.length === 0)
    return {
      originalSelection: [],
      expandedSelection: [],
    };

  // 计算原始选择区域的边界
  let minRow = Infinity,
    maxRow = -Infinity;
  let minCol = Infinity,
    maxCol = -Infinity;

  for (const point of selectedPoints) {
    minRow = Math.min(minRow, point.row);
    maxRow = Math.max(maxRow, point.row);
    minCol = Math.min(minCol, point.col);
    maxCol = Math.max(maxCol, point.col);
  }

  // 递归扩展边界，包含所有相关合并单元格
  const expandedBoundary = expandBoundaryWithMergedCells(
    { minRow, maxRow, minCol, maxCol },
    mergedCells
  );

  //  生成扩展后的选择区域
  const expandedSelection = [];
  for (
    let row = expandedBoundary.minRow;
    row <= expandedBoundary.maxRow;
    row++
  ) {
    for (
      let col = expandedBoundary.minCol;
      col <= expandedBoundary.maxCol;
      col++
    ) {
      expandedSelection.push({ row, col });
    }
  }

  // 返回原始选择区域和扩展后的选择区域
  return {
    originalSelection: [...selectedPoints],
    expandedSelection,
  };
}

// 递归扩展边界以包含所有相关合并单元格
function expandBoundaryWithMergedCells(boundary, mergedCells) {
  let changed = true;
  // 使用Set记录已处理的合并单元格，避免无限递归
  const processedMerged = new Set();

  while (changed) {
    changed = false;

    for (const merged of mergedCells) {
      // 跳过已处理的合并单元格
      if (processedMerged.has(merged)) continue;

      const { start, end } = merged;
      // 检查合并单元格是否与当前边界重叠
      const rowOverlap =
        start.row <= boundary.maxRow && end.row >= boundary.minRow;
      const colOverlap =
        start.col <= boundary.maxCol && end.col >= boundary.minCol;

      if (rowOverlap && colOverlap) {
        // 扩展边界以包含整个合并单元格
        boundary.minRow = Math.min(boundary.minRow, start.row);
        boundary.maxRow = Math.max(boundary.maxRow, end.row);
        boundary.minCol = Math.min(boundary.minCol, start.col);
        boundary.maxCol = Math.max(boundary.maxCol, end.col);

        // 标记此合并单元格已处理
        processedMerged.add(merged);
        changed = true;
      }
    }
  }

  return boundary;
}

function calculateDivPosition(cells) {
  if (cells.length === 0) {
    return { width: 0, height: 0, top: 0, left: 0 };
  }

  // 找出最小和最大的行、列号
  let minRow = Infinity,
    maxRow = -Infinity;
  let minCol = Infinity,
    maxCol = -Infinity;

  let totalHeight = 0,
    totalWidth = 0;
  cells.forEach((cell) => {
    minRow = Math.min(minRow, cell.row);
    maxRow = Math.max(maxRow, cell.row + 1);
    minCol = Math.min(minCol, cell.col);
    maxCol = Math.max(maxCol, cell.col + 1);
  });

  // 计算选中的宽度 高度
  for (let i = minRow; i <= maxRow - 1; i++) {
    totalHeight += excel.data[i].height;
  }
  for (let i = minCol; i <= maxCol - 1; i++) {
    totalWidth += excel.columns[i].width;
  }
  console.log("计算合并区域", {
    rowspan: maxRow - minRow,
    colspan: maxCol - minCol,
  });
  // 计算div的尺寸和位置
  // const width = (maxCol - minCol) * colWidth;
  // const height = (maxRow - minRow) * rowHeight;

  const width = totalWidth;
  const height = totalHeight;

  const top = excel.data
    .filter((i, index) => index <= minRow - 1)
    .map((i) => i.height)
    .reduce((a, b) => a + b, 0);
  const left = excel.columns
    .filter((i, index) => index <= minCol - 1)
    .map((i) => i.width)
    .reduce((a, b) => a + b, 0);
  console.log("更新选择区域 left,top,width,height", left, top, width, height);
  return {
    width: width + 1 + "px",
    height: height + 1 + "px",
    top: top - 1 + "px",
    left: left - 1 + "px",
  };
}

function updatePosition() {
  const position = calculateDivPosition(state.selectedCells);
  state.selectedStyle = position;
}

watch(
  () => state.selectedCells,
  (list) => {
    updatePosition();
  },
  {
    deep: true,
  }
);

function scrollHandler(event, type) {
  const target = event.target;
  const scrollLeft = target.scrollLeft;
  const scrollTop = target.scrollTop;
  if (type == "rowBody") {
    // 更新行头和列头的滚动位置
    const rowHeader = document.querySelector(
      ".row-header .rows-header-scroller"
    );
    if (rowHeader) {
      rowHeader.scrollTop = scrollTop;
    }
    const colHeader = document.querySelector(
      ".col-header .cols-header-scroller"
    );
    if (colHeader) {
      colHeader.scrollLeft = scrollLeft;
    }
  } else if (type == "row") {
    // debugger
    const rowBody = document.querySelector(".table-body-wrap .table-body");
    if (rowBody) {
      rowBody.scrollTop = scrollTop;
    }
  }
}

const handleResizeWidth = (e) => {
  if (!state.isUpdateWH) return;
  let deltaX = e.clientX - state.updateSize.startX;
  if (deltaX <= -50) {
    deltaX = -50;
  }
  state.updateWStyle.transform = `translateX(${deltaX}px)`;
  state.updateSize.endX = state.updateSize.width + deltaX;
};

const stopResizeWidth = () => {
  state.isUpdateWH = false;
  const index = state.updateSize.colIndex - 1;
  const width = state.updateSize.endX;
  excel.columns[index].width = width;
  document.removeEventListener("mousemove", handleResizeWidth);
  document.removeEventListener("mouseup", stopResizeWidth);

  // 更新 data row 对应的 width
  excel.data.forEach((item) => {
    const key = item.getKeyByIndex(state.updateSize.colIndex);
    item[key].width = width;
    // console.log(item[key].col,width)
  });
  // 更新选择的位置区域
  updatePosition();
  nextTick(() => {
    state.updateSize = null;
  });
};

// 更新单元格 width
function stareUpdateWidth(e, col, colIndex) {
  console.log(e);
  state.isUpdateWH = true;

  let startWidth = excel.columns
    .filter((i, index) => index <= colIndex)
    .map((i) => i.width)
    .reduce((a, b) => a + b, 0);
  startWidth += 32 + 250;

  console.log("开始left", startWidth);

  state.updateWStyle = {
    position: "fixed",
    top: 96 + "px",
    height: "calc(100vh - 136px)",
    left: startWidth + "px",
    zIndex: 10,
  };
  state.updateSize = {
    ...col,
    startX: startWidth,
  };

  document.addEventListener("mousemove", handleResizeWidth);
  document.addEventListener("mouseup", stopResizeWidth);
}

function handleResizeHeight(e) {
  if (!state.isUpdateWH) return;
  let deltaY = e.clientY - state.updateSize.startY;
  if (deltaY <= -20) {
    deltaY = -20;
  }
  state.updateHStyle.transform = `translateY(${deltaY}px)`;
  state.updateSize.endY = state.updateSize.height + deltaY;
}

function stopResizeHeight() {
  state.isUpdateWH = false;
  const index = state.updateSize.rowIndex;
  const height = state.updateSize.endY;
  excel.data[index].height = height;
  document.removeEventListener("mousemove", handleResizeHeight);
  document.removeEventListener("mouseup", stopResizeHeight);

  // 更新 data row 对应的 height
  // excel.columns.forEach((item)=>{
  //   const key = item.getKeyByIndex(state.updateSize.rowIndex)
  //   item[key].height = height
  //   // console.log(item[key].col,width)
  // })
  // 更新选择的位置区域
  updatePosition();
  nextTick(() => {
    state.updateSize = null;
  });
}

// 更新单元格 height----
function stareUpdateHeight(e, row, rowIndex) {
  state.isUpdateWH = true;
  let startHeight = excel.data
    .filter((row, index) => index <= rowIndex)
    .map((i) => i.height)
    .reduce((a, b) => a + b, 0);
  startHeight += 73 + 48;
  console.log("开始top", startHeight);
  state.updateHStyle = {
    position: "fixed",
    top: startHeight + "px",
    width: "calc(100vw - 250px)",
    left: 250 + "px",
    zIndex: 80,
  };
  state.updateSize = {
    startY: startHeight,
    rowIndex,
    height: row.height,
  };

  console.log(state.updateSize);
  document.addEventListener("mousemove", handleResizeHeight);
  document.addEventListener("mouseup", stopResizeHeight);
}

onMounted(() => {
  document.addEventListener("mouseup", () => {
    state.isDragging = false;
    state.isUpdateWH = false;
    state.updateHStyle = null;
    state.updateWStyle = null;
    // document.removeEventListener('mousemove', () => {
    //   // console.log('mousemove removed')
    // });
  });  
});


defineExpose({
    getContent
})

</script>

<style lang="less" scoped>
.manuscript-design{
  display: flex;
  position: relative;
}


.online-excel {
  width: 100vw;
  flex: 1;
  height: calc(100vh - 48px);
  background-color: #eee;
  // padding-left: 250px;
  .draw-area {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .draw-area-2 {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .toolbar {
    min-height: 48px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    // background: #f7f8fa;
    box-shadow: 1px 0 0 0 #f7f8fa;
    padding: 0 8px;
    overflow: hidden;
  }

  .excel-content {
    width: 100%;
    position: relative;
    flex: 1;
  }
}

.excel-body {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);

  position: relative;
  width: 100%;
  height: 100%;

  .main-box * {
    box-sizing: border-box;
  }

  .designer-layout {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    padding-left: 36px;
    // background-color: rgba(15, 28, 53, .1);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }

  .row-header {
    position: absolute;
    width: 36px;
    overflow: hidden;
    top: 28px;
    left: 0;

    .rows-header-scroller {
      width: 80px;
      overflow-y: auto;
      overflow-x: hidden;

      .rows-header__item {
        position: relative;
        height: 28px;
        line-height: 28px;
        border-width: 0 1px 1px 1px;
        border-color: #dadce5;
        border-style: solid;
        text-align: center;
        width: 36px;
        background-color: #ecf0f6;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        flex-direction: column;
        cursor: e-resize;

        .rows-header__text {
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          flex: 1;
          width: 100%;

          display: flex;
          align-items: center;
          justify-content: center;
        }

        .rows-header__selector {
          bottom: 0;
          left: 0;
          height: 4px;
          width: 100%;
          cursor: row-resize;
          position: absolute;
          -webkit-user-select: none;
          -moz-user-select: none;
          user-select: none;
          border-width: 0;
          border-style: dashed;
          border-color: #107fff;
          opacity: 0;
          z-index: 66;
          margin-bottom: -1px;

          &::before {
            content: "";
            position: absolute;
            top: -6px;
            left: 0;
            right: 0;
            bottom: -6px;
          }

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 36px;
            height: 4px;
            background-color: #107fff;
          }

          &:hover {
            opacity: 1;
            margin-bottom: 0;
          }
        }

        .rows-header__selector.active {
          opacity: 1;
          margin-bottom: 0;
          border-bottom-width: 1px;
          transform: translateY(50%);

          &:hover {
            border-color: none !important;
          }
        }
      }

      .rows-header__item.header-selected {
        background-color: #b3e0ff;
      }
    }
  }

  .col-header {
    height: 28px;
    position: relative;
    z-index: 55;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    overflow: hidden;
    background-color: #ecf0f6;

    .cols-header-scroller {
      overflow-x: auto;
      overflow-y: hidden;
      height: 80px;
      display: flex;

      .cols-header__item {
        position: relative;
        width: 80px;
        height: 28px;
        border-width: 1px 1px 1px 0;
        border-color: #dadce5;
        border-style: solid;
        line-height: 28px;
        text-align: center;
        flex-shrink: 0;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        cursor: s-resize;

        .cols-header__text {
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          flex: 1;
        }

        .cols-header__selector {
          right: 0;
          top: 0;
          height: 100%;
          width: 4px;
          cursor: col-resize;
          position: absolute;
          -webkit-user-select: none;
          -moz-user-select: none;
          user-select: none;
          border-width: 0;
          border-style: dashed;
          border-color: #107fff;
          opacity: 0;
          z-index: 66;
          margin-bottom: -1px;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: -6px;
            right: -6px;
            bottom: 0;
          }

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 28px;
            background-color: #107fff;
          }

          &:hover {
            opacity: 1;
            margin-right: 0;
          }
        }

        .cols-header__selector.active {
          opacity: 1;
          margin-right: 0;
          border-right-width: 1px;
          transform: translateX(50%);

          &:hover {
            border-color: none !important;
          }
        }
      }

      .cols-header__item.header-selected {
        background-color: #b3e0ff;
      }
    }
  }

  .corner-header {
    height: 28px;
    width: 36px;
    border: 1px solid #dadce5;
    background-color: #ecf0f6;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 12;
    box-sizing: border-box;
  }

  .table-body-wrap {
    display: table;

    .table-body {
      z-index: 11;
      position: relative;
      width: 100%;
      height: 100%;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dadce5;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f7f8fa;
      }

      table {
        border-spacing: 0;
        border-collapse: collapse;
        font-size: 14px;
      }

      tr {
        padding: 0;
        margin: 0;
        border-color: #dadce5;
      }

      .content-table {
        cursor: cell;
        margin-top: -1px;
        margin-left: -1px;
        border: none;

        .row {
          height: 28px;

          tr {}

          td.cell {
            border-bottom: 1px solid #dadce5;
            border-right: 1px solid #dadce5;
            position: relative;
            user-select: none;
            overflow: hidden;

            .wrap {
              overflow: hidden;
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              z-index: 44;

              .cell-data {
                display: table;
                width: 100%;
                height: 100%;

                .content {
                  height: 100%;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-word;
                  white-space: break-spaces;
                  font-size: 14px;
                  font-weight: 400;
                  text-decoration: none;
                  display: table-cell;
                  text-align: center;
                  vertical-align: middle;
                  -webkit-user-select: none;
                  -moz-user-select: none;
                  user-select: none;
                  margin: 0;
                  padding: 0;
                  -webkit-text-decoration-skip-ink: none;
                  text-decoration-skip-ink: none;
                  line-height: 1.5;
                  font-family: STHeiti, SimHei, 华文黑体, 黑体;
                }

                .content-edit {
                  user-select: auto;
                  padding: 0 4px;
                  outline: none;
                  border: none;
                  resize: none;
                }
              }
            }
          }

          .cell.hidden {
            display: none;
          }
          .cell.field-content{
            .content{
              color: #107fff;
            }
          }
        }
      }

      .border-table {
        margin-top: -1px;
        margin-left: -1px;
        border: none;
        border-spacing: 0;
        border-collapse: collapse;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 22;
        background: transparent;
        pointer-events: none;

        td.cell {
          position: relative;
          border-bottom: 1px solid transparent;
          border-right: 1px solid transparent;
        }

        .cell.border-right {
          border-right-width: 1px;
        }


        .cell.border-right.border-width-small {
          border-right-width: 1px;
        }

        .cell.border-right.border-width-middle {
          border-right-width: 2px;
        }

        .cell.border-right.border-width-large {
          border-right-width: 3px;
        }

        // ---
        .cell.border-bottom {
          border-right-width: 1px;
        }

        .cell.border-bottom.border-width-small {
          border-bottom-width: 1px;
        }

        .cell.border-bottom.border-width-middle {
          border-bottom-width: 2px;
        }

        .cell.border-bottom.border-width-large {
          border-bottom-width: 3px;
        }

      }

      .background-layer {
        .table-dashed-line {
          position: absolute;
          left: 0;
          top: 0;
          border: 1px dashed #000;
          background-color: #fff;
          z-index: -1;
          pointer-events: none;
        }

        .table-unprintable-hint {
          position: absolute;
          top: 0;
          z-index: 100;
          padding-left: 60px;
          line-height: 48px;
          width: 220px;
          height: 48px;
          color: #777f8d;
          font-size: 18px;
          pointer-events: none;
        }
      }

      .interact-layer {
        .cell-selected-wrap {
          .selected-mask {
            position: absolute;
            background-color: rgba(245, 247, 250, 0.5);
            z-index: 33;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
            pointer-events: none;
            border: 2px solid #107fff;
          }
        }

        .cell-clip-wrap {
          .clip-mask {
            position: absolute;
            background-color: rgba(245, 247, 250, 0.5);
            z-index: 33;
            user-select: none;
            pointer-events: none;

            rect {
              stroke: #107fff;
              stroke-width: 4px;
              stroke-dasharray: 10;
              fill: transparent;
            }
          }
        }
      }
    }
  }
}

.excel-sheet {
  position: absolute;
  // width: calc(100vw - 536px);
  height: 30px;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 12;
  background: #d7d7d7;
  overflow: hidden;
  overflow-x: auto;
  white-space: nowrap;

  padding: 0 36px;

  .sheet-item {
    display: inline-block;
    padding: 0 20px;
    line-height: 30px;
    border-right: 1px dashed #dadce5;
    cursor: pointer;
    background: #f2f2f2;
    font-size: 12px;

    .sheet-page {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      position: relative;
      -moz-user-select: none;
      user-select: none;
      -webkit-user-select: none;
    }
  }

  .sheet-item.active {
    background: #b3e0ff;
    color: #315efb;
    font-size: 15px;
  }
}


:deep(.ant-input-number-handler-wrap){
  display: none;
}



</style>
