<template>
    <div class="preview">
        <div class="excel-content" style="width: 210mm;height: 297mm;padding: 20mm 17mm;">
            <div class="runtime-layout">
                <div class="preview__page-corner lb"></div>
                <div class="preview__page-corner lt"></div>
                <div class="preview__page-corner rb"></div>
                <div class="preview__page-corner rt"></div>
                <div class="runtime-page-header"></div>
                <div class="runtime-page-footer"></div>
                <div class="excel-body">
                    <div class="main-box designer-layout">
                        <div class="content-layer">
                            <div class="table-body-wrap">
                                <div class="table-body" style="height:100%;">
                                    <table class="content-table" tabindex="0">
                                        <thead>
                                            <tr>
                                                <th :style="{ width: `${col.width}px`, maxWidth: `${col.width}px`, minWidth: `${col.width}px` }"
                                                    :data-col="colIndex + 1" v-for="(col, colIndex) in excel.columns"
                                                    :key="colIndex">
                                                </th>
                                            </tr>
                                        </thead>
                                        <!--  -->
                                        <tbody>
                                            <template v-for="(row, rowIndex) in excel.data" :key="rowIndex">
                                                <tr class="row" style="height: 3px;" v-if="rowIndex == 0">
                                                    <td class="cell" :data-col="col.colIndex" v-for="(col, colIndex) in row"
                                                        :class="`${col.className}`"
                                                        :colspan="col.colspan"
                                                        :style="col.styleBorder" :key="colIndex">
                                                    </td>
                                                </tr>
                                            </template>

                                            <tr class="row" :data-row="rowIndex + 1"
                                                v-for="(row, rowIndex) in excel.data" :key="rowIndex"
                                                :style="{ height: `${row.height}px` }">
                                                <td class="cell" :data-col="col.colIndex" v-for="(col, colIndex) in row"
                                                    :rowspan="col.rowspan" :colspan="col.colspan" :class="col.className"
                                                    :key="colIndex">
                                                    <!-- content -->
                                                    {{ row.content }}
                                                    <!--  -->
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <!-- border -->
                                    <table class="border-table">
                                        <thead>
                                            <tr>
                                                <!-- <th  :data-col="0" class="row" style="width: 3px; min-width: 3px; max-width: 3px;"></th> -->
                                                <th :style="{
                                                    width: `${col.width}px`,
                                                    maxWidth: `${col.width}px`,
                                                    minWidth: `${col.width}px`,
                                                }" :data-col="colIndex + 1" v-for="(col, colIndex) in excel.columns"
                                                    :key="colIndex"></th>
                                            </tr>
                                        </thead>
                                        <tbody>                                            

                                            <!-- <template v-for="(row, rowIndex) in excel.data" :key="rowIndex">
                                                <tr class="row" style="height: 3px;" v-if="rowIndex == 0">
                                                     <template v-for="(col, colIndex) in row" :key="colIndex">
                                                        <td class="cell" v-if="colIndex == 'A'"  :data-col="0" style="width: 3px;" :rowspan="col.rowspan + 1" :style="col.styleBorder" :class="`${col.className} ${col.classNameBorder}`">
                                                            
                                                        </td>
                                                        <td class="cell" :data-col="col.colIndex"
                                                            :class="`${col.className} ${col.classNameBorder}`"
                                                            :colspan="col.colspan"
                                                            :style="col.styleBorder" >
                                                        </td>
                                                     </template>                                                    
                                                </tr>
                                            </template> -->

                                            <tr class="row" :data-row="rowIndex + 1"
                                                v-for="(row, rowIndex) in excel.data" :key="rowIndex"
                                                :style="{ height: `${row.height}px` }">
                                                <template v-for="(col, colIndex) in row" :key="colIndex">
                                                    <td class="cell" :data-col="col.colIndex"
                                                        :class="`${col.className} ${col.classNameBorder}`"
                                                        :rowspan="col.rowspan" :colspan="col.colspan"
                                                        :style="col.styleBorder">
                                                    </td>
                                                 </template>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue';
import Excel from "./core";
const excel = reactive(new Excel());

const props = defineProps({
    options: {
        type: Object,
        default: () => ({})
    }
})

const state = reactive({
    options: {}
})

function initExcel(options) {
    const { merges = [], cells = {}, cols = {}, rows = {} } = props.options
    state.merges = [...merges]

    for (let len in cols) {
        const width = cols[len]
        if (len == 'length') {
            continue
        } else {
            excel.columns[len - 1].width = width
        }
    }
    for (let len in rows) {
        const height = rows[len]
        if (len == 'length') {
            continue
        } else {
            excel.data[len - 1].height = height
        }
    }
    let index = 0
    for (let key in cells) {
        if (Object.keys(cells).length == index) {
            break
        }
        const cell = cells[key]
        for (let i in excel.data) {
            if (Object.keys(cells).length == index) {
                break
            }
            const row = excel.data[i]
            for (let k in row) {
                const col = row[k]
                if (col.col == key) {
                    // 
                    for (let j in cell) {
                        col[j] = cell[j]
                    }
                    index++
                }
                continue;
            }
        }
    }
}

watch(() => props.options, (newVal) => {
    if (newVal) {
        state.options = newVal
        console.log('预览参数', state.options)
        initExcel(state.options)
    }
}, { immediate: true, deep: true })

</script>

<style lang="less" scoped>
.preview {
    position: relative;
    border: 1px solid red;

    .excel-content {
        // overflow: hidden;
        background-color: #fff !important;

        * {
            box-sizing: border-box;
        }
    }

    .runtime-layout {
        position: relative;
        width: 100%;
        height: 100%;

        .runtime-page-header {
            top: -10mm;
            position: absolute;
            height: 32px;
            width: 100%;
            font-size: 14px;
            display: flex;
            z-index: 100;
            line-height: 32px;
        }

        .runtime-page-footer {
            bottom: -10mm;
            position: absolute;
            height: 32px;
            width: 100%;
            font-size: 14px;
            display: flex;
            z-index: 100;
            line-height: 32px;
        }

        .table-body {
            z-index: 11;
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            border: none;
            border-spacing: 0;
            border-collapse: collapse;
        }

        .preview__page-corner {
            position: absolute;
            width: 27px;
            height: 27px;
            border-width: 1px;
            border-color: #e3e6ec;
            border-style: solid;
        }

        .preview__page-corner.lb {
            left: -27px;
            bottom: -27px;
            border-width: 1px 1px 0 0;
        }

        .preview__page-corner.lt {
            left: -27px;
            top: -27px;
            border-width: 0 1px 1px 0;
        }

        .preview__page-corner.rb {
            right: -27px;
            bottom: -27px;
            border-width: 1px 0 0 1px;
        }

        .preview__page-corner.rt {
            right: -27px;
            top: -27px;
            border-width: 0 0 1px 1px;
        }
    }

    .excel-body {
        height: 100%;
        overflow: hidden;
        .content-layer {}
    }


    .table-body-wrap {
        display: table;

        .table-body {
            z-index: 11;
            position: relative;
            width: 100%;
            height: 100%;
            overflow: auto;

            &::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }

            &::-webkit-scrollbar-thumb {
                background-color: #dadce5;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-track {
                background-color: #f7f8fa;
            }

            table {
                border-spacing: 0;
                border-collapse: collapse;
                font-size: 14px;
            }

            tr {
                padding: 0;
                margin: 0;
                border-color: #dadce5;
            }

            .content-table {
                cursor: pointer;
                margin-top: -2px;
                margin-left: -2px;
                border: none;

                .row {
                    height: 28px;

                    tr {}

                    td.cell {
                        // border-bottom: 1px solid #dadce5;
                        // border-right: 1px solid #dadce5;
                        position: relative;
                        user-select: none;
                        overflow: hidden;
                    }

                    .cell.hidden {
                        display: none;
                    }
                }
            }


            .border-table {
                margin-top: -2px;
                margin-left: -2px;
                border: none;
                border-spacing: 0;
                border-collapse: collapse;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 22;
                background: transparent;
                pointer-events: none;
                // TODO：
                border-left: 1px solid #000;
                border-top: 1px solid #000;

                td.cell {
                    position: relative;
                    border-bottom: 1px solid transparent;
                    border-right: 1px solid transparent;
                }

                .cell.border-right {
                    border-right-width: 1px;
                }


                .cell.border-right.border-width-small {
                    border-right-width: 1px;
                }

                .cell.border-right.border-width-middle {
                    border-right-width: 2px;
                }

                .cell.border-right.border-width-large {
                    border-right-width: 3px;
                }

                // ---
                .cell.border-bottom {
                    border-right-width: 1px;
                }

                .cell.border-bottom.border-width-small {
                    border-bottom-width: 1px;
                }

                .cell.border-bottom.border-width-middle {
                    border-bottom-width: 2px;
                }

                .cell.border-bottom.border-width-large {
                    border-bottom-width: 3px;
                }

            }
        }
    }
}
</style>