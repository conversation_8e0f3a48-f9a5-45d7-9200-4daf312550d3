function nonEnumerable(target, name, descriptor) {
  descriptor.enumerable = false;
  return descriptor;
}

class Config {
  constructor({
    style = {},
    width = 80,
    height = 28,
    columns = [],
    data = [],
  }) {
    const defaultStyle = {
      font: {
        bold: false,
        italic: false,
        size: 12,
        family: "Arial",
        color: "#000000",
      },
      background: "#FFFFFF",
      border: {
        top: { style: "none", color: "#000000" },
        right: { style: "none", color: "#000000" },
        bottom: { style: "none", color: "#000000" },
        left: { style: "none", color: "#000000" },
      },
      alignment: {
        horizontal: "left", // left, center, right
        vertical: "top", // top, middle, bottom
      },
      format: "general", // 数字格式: general, number, percent, date, etc
    };
    this.style = { ...defaultStyle, ...style };
    this.width = width;
    this.height = height;
    this.columns = columns;
    this.data = data;
  }
}

class Row {
  constructor(height = 0) {
    this._height = height;
    Object.defineProperty(this, "_height", {
      enumerable: false, // 设置为不可枚举
      configurable: true,
    });
  }
  get height() {
    return this._height;
  }
  set height(value) {
    if (typeof value === "number" && value >= 0) {
      this._height = value;
    } else {
      throw new Error("Height must be a non-negative number");
    }
  }

  getExcelColumnLetters(n) {
    let result = "";
    while (n > 0) {
      let remainder = (n - 1) % 26;
      result = String.fromCharCode(65 + remainder) + result;
      n = Math.floor((n - 1) / 26);
    }
    return result;
  }
  getKeyByIndex(index) {
    return this.getExcelColumnLetters(index);
  }
}

class Utils extends Config {
  constructor(opt) {
    super(opt);
  }
  // 获取指定数字的Excel列字母 ["A", "B", ..., "Z", "AA", "AB", "AD"]
  generateExcelColumnSequence(count) {
    const sequence = [];
    for (let i = 1; i <= count; i++) {
      sequence.push(this.getExcelColumnLetters(i));
    }
    return sequence;
  }
  // 获取Excel列字母 // 1=> 'A'
  getExcelColumnLetters(n) {
    let result = "";
    while (n > 0) {
      let remainder = (n - 1) % 26;
      result = String.fromCharCode(65 + remainder) + result;
      n = Math.floor((n - 1) / 26);
    }
    return result;
  }
  // 获取Excel列字母对应的数字
  getExcelColumnNumber(letters) {
    let result = 0;
    for (let i = 0; i < letters.length; i++) {
      const char = letters.toUpperCase().charCodeAt(i);
      if (char < 65 || char > 90) {
        throw new Error("Invalid Excel column letters");
      }
      result = result * 26 + (char - 64);
    }
    return result;
  }
  // 生成数字数组
  generateNumberArray(start, end) {
    if (end == undefined) {
      end = start;
      start = 0;
    }
    const length = end - start + 1;
    return Array.from({ length }, (_, i) => start + i);
  }
  _initColumns() {
    this.columns = this.generateExcelColumnSequence(26).map((item, index) => {
      return {
        value: item,
        colIndex: index + 1,
        width: this.width,
        height: this.height,
        style: this.style,
      };
    });
    return this;
  }
  _initData() {
    this.data = this.generateNumberArray(1, 60).map((item, index) => {
      return this.createRow(index + 1);
    });
  }

  createRow(rowIndex) {
    const row = new Row(this.height);

    this.columns.forEach((col, index) => {
      const obj = {
        value: "",
        width: col.width,
        height: this.height,
        colIndex: index + 1,
        rowIndex,
        rowspan: null,
        colspan: null,
        style: {},
        className: "",
        // 边框特有
        classNameBorder: "",
        styleBorder: {},
        col: col.value + rowIndex,
        fieldInfo: {},
      };
      row[col.value] = obj;
    });
    return row;
  }
  createCol() {
    const len = this.columns.length + 1;
    const col = this.getExcelColumnLetters(len);
    return {
      value: col,
      colIndex: len + 1,
      width: this.width,
      height: this.height,
      style: {},
    };
  }
}
export default class Excel extends Utils {
  constructor(options = {}) {
    super(options);
    // 表格数据

    // 样式配置

    // 行列配置

    // 选择区域
    this.selection = {
      // 样式
      style: {},
      // 单元格
      cells: [],
      // 开始单元格
      start: null,
      // 结束单元格
      end: null,
    };
    this._initColumns()._initData();
  }
}

export const fieldInfo = {
  systemList: [
    {
      label: "标题",
      value: "${标题}",
      fieldType: "input",
      fieldName: "title",
      source: "system",
      placeholder: "请输入",
      maxlength: 50,
      required: true,
    },
    {
      label: "紧急程度",
      value: "${紧急程度}",
      fieldType: "select",
      fieldName: "urgencyLevel",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
    {
      label: "密级",
      value: "${密级}",
      fieldType: "select",
      fieldName: "securityClassification",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
    {
      label: "发文字号",
      value: "${发文字号}",
      fieldType: "input",
      fieldName: "docNo",
      source: "system",
      placeholder: "请输入",
      maxlength: 20,
      required: true,
    },
    {
      label: "拟文日期",
      value: "${拟文日期}",
      fieldType: "date",
      fieldName: "draftDate",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
    {
      label: "发文单位",
      value: "${发文单位}",
      fieldType: "input",
      fieldName: "issuingOrg",
      source: "system",
      placeholder: "请输入",
      maxlength: 20,
      required: true,
    },
    {
      label: "文件类型",
      value: "${文件类型}",
      fieldType: "select",
      fieldName: "fileType",
      source: "system",
      placeholder: "请选择",
      required: true,
    },
    {
      label: "内容摘要",
      value: "${内容摘要}",
      fieldType: "textarea",
      fieldName: "content",
      source: "system",
      placeholder: "请输入",
      maxlength: 200,
      required: true,
    },
  ],
  customList: [
    {
      label: "文本框",
      value: "${文本框}",
      fieldType: "input",
      fieldName: "customText",
      source: "custom",
      placeholder: "请输入",
      maxlength: 100,
      required: false,
    },
  ],
  flowList: [],
  docReceiptsList:[
    {
      label: "收文日期",
      value: "${收文日期}",
      fieldType: "date",
      fieldName: "draftDate",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
    {
      label: "来文字号",
      value: "${来文字号}",
      fieldType: "input",
      fieldName: "docNo",
      source: "system",
      placeholder: "请输入",
      maxlength: 20,
      required: true,
    },
    {
      label: "来文单位",
      value: "${来文单位}",
      fieldType: "input",
      fieldName: "issuingOrg",
      source: "system",
      placeholder: "请输入",
      maxlength: 20,
      required: true,
    },
    {
      label: "份数",
      value: "${份数}",
      fieldType: "input",
      fieldName: "len",
      source: "system",
      placeholder: "请输入",
      maxlength: 20,
      required: true,
    },
    {
      label: "文件类型",
      value: "${文件类型}",
      fieldType: "select",
      fieldName: "fileType",
      source: "system",
      placeholder: "请选择",
      required: true,
    },
    {
      label: "标题",
      value: "${标题}",
      fieldType: "input",
      fieldName: "title",
      source: "system",
      placeholder: "请输入",
      maxlength: 50,
      required: true,
    },
    {
      label: "内容摘要",
      value: "${内容摘要}",
      fieldType: "textarea",
      fieldName: "content",
      source: "system",
      placeholder: "请输入",
      maxlength: 200,
      required: true,
    },
    {
      label: "紧急程度",
      value: "${紧急程度}",
      fieldType: "select",
      fieldName: "urgencyLevel",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
    {
      label: "密级",
      value: "${密级}",
      fieldType: "select",
      fieldName: "securityClassification",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
  ],
  memoDraftsList:[
    {
      label: "拟稿日期",
      value: "${拟稿日期}",
      fieldType: "date",
      fieldName: "draftDate",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
    {
      label: "签报字号",
      value: "${签报字号}",
      fieldType: "input",
      fieldName: "docNo",
      source: "system",
      placeholder: "请输入",
      maxlength: 20,
      required: true,
    },
    {
      label: "签报部门",
      value: "${签报部门}",
      fieldType: "input",
      fieldName: "issuingOrg",
      source: "system",
      placeholder: "请输入",
      maxlength: 20,
      required: true,
    },
     {
      label: "签报类型",
      value: "${签报类型}",
      fieldType: "select",
      fieldName: "fileType",
      source: "system",
      placeholder: "请选择",
      required: true,
    },
    {
      label: "标题",
      value: "${标题}",
      fieldType: "input",
      fieldName: "title",
      source: "system",
      placeholder: "请输入",
      maxlength: 50,
      required: true,
    },
    {
      label: "内容摘要",
      value: "${内容摘要}",
      fieldType: "textarea",
      fieldName: "content",
      source: "system",
      placeholder: "请输入",
      maxlength: 200,
      required: true,
    },
    {
      label: "紧急程度",
      value: "${紧急程度}",
      fieldType: "select",
      fieldName: "urgencyLevel",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
    {
      label: "密级",
      value: "${密级}",
      fieldType: "select",
      fieldName: "securityClassification",
      source: "system",
      placeholder: "请选择",
      required: false,
    },
  ]
};

export const fieldSettingMap = {
  required:{
    label:"是否必填",
    component:"switch"
  },
  maxlength:{
    label:"最大长度",
    component:"input"
  },
  placeholder:{
    label:"提示文字",
    component:"input"
  }
}
