<template>
    <div class="flow-setting">
        <div class="flow-steps">
            <a-steps v-model:current="state.current" type="navigation" :items="state.stepsList"></a-steps>
        </div>
        <div class="flow-from">
            <a-form :model="state.item" ref="formRef" :rules="rules">
                <a-form-item label="流程名称" style="margin-bottom: 20px;">
                    {{ title }}
                </a-form-item>
                <template v-if="state.item.id != 'root'">
                     <a-form-item label="流程状态" style="margin-bottom: 20px;">
                    <a-switch v-model:checked="state.item.nodeStatus" />
                </a-form-item>
                <a-form-item label="处理人员" name="assignedType" style="margin-bottom: 20px;">
                    <a-radio-group v-model:value="state.item.assignedType" style="margin-top: 5px;">
                        <a-radio :value="item.value" v-for="(item, idx) in assignedTypeList" :key="idx">
                            {{ item.label }}
                        </a-radio>
                    </a-radio-group>
                    <template v-if="state.item.assignedType == 'ASSIGN_USER'">
                        <div style="margin-top: 20px;">
                            <a-button @click="openModelPerson('people')">
                                {{ state.item.assignedUser?.length > 0 ? state.item.assignedUser?.map(item => item.name).join('、') : '选择人员' }}
                            </a-button>
                        </div>
                    </template>
                    <template v-if="state.item.assignedType == 'ROLE'">
                        <div style="margin-top: 20px;">
                            <a-button @click="openModelPerson('all')">
                                {{ state.item.role?.length > 0 ? state.item.role?.map(item => item.name).join('、') : '选择系统角色' }}
                            </a-button>
                        </div>
                    </template>
                    <template v-if="state.item.assignedType == 'LEADER'">
                        <div style="margin-top: 20px;">
                            <div style="color:#606266">指定主管</div>
                            <div style="padding: 10px 0;display: flex;align-items: center;">
                                <span>发起人的第</span>
                                <a-input-number style="width: 100px;margin: 0 10px;" v-model:value="state.item.level"
                                    :min="1" :max="99" />
                                <span>级主管</span>
                            </div>
                            <div>👉直接主管为 第 1 级主管</div>
                        </div>
                    </template>
                    <template v-if="state.item.assignedType == 'LEADER_TOP'">
                        <div style="margin-top: 20px;">
                            <div style="color:#606266">连续多级主管审批终点</div>
                            <a-radio-group v-model:value="state.item.endCondition">
                                <a-radio style="display:flex;height:30px;line-height:30px"
                                    value="TOP">从直接主管到最高层级主管</a-radio>
                                <a-radio style="display:flex;height:30px;line-height:30px" value="LEAVE">
                                    同时不超过发起人向上的
                                    <div style="display: inline-block;padding-left: 30px;"
                                        v-if="state.item.endCondition === 'LEAVE'">
                                        <div style="display: flex; align-items: center;">
                                            <span>第</span>
                                            <a-input-number v-model:value="state.item.endLevel" :min="1" :max="99"
                                                style="width: 100px; margin:0 10px" />
                                            <span>级主管</span>
                                        </div>
                                    </div>
                                </a-radio>

                            </a-radio-group>
                        </div>
                    </template>
                </a-form-item>
                <a-form-item label="多人审批时审批方式"></a-form-item>
                <a-form-item style="margin-bottom: 20px;" :colon="false">
                    <template #label> </template>
                    <a-radio-group v-model:value="state.item.mode">
                        <a-radio style="display:flex;height:30px;line-height:30px" :value="item.value"
                            v-for="(item, idx) in modeList" :key="idx">{{ item.label }}</a-radio>
                    </a-radio-group>
                </a-form-item>
                </template>
            </a-form>
        </div>
        <!-- 选择人员 -->
        <PersonSelectionControl ref="personRef" :type="state.toggleTabsType" :tabs="state.tabsSelection"
            @toggleLevel="toggleLevel" @submit="submitPerson" @search="searchSelect" @toggleTabs="toggleTabs"
            :maxOptional="20" :selected="state.checkUserList" />
        <!--  -->
    </div>
</template>

<script setup>
import { onMounted, reactive, computed, ref } from 'vue';
import PersonSelectionControl from "@/components/PersonSelectionControl/index.vue"

const route = useRoute()
const emit = defineEmits(['update'])
const personRef = ref()
const stateDataSource = ref([])
const schoolRollTree = ref([])
const props = defineProps({
    data: {
        type: Array,
        default: () => []
    }
})

const formRef = ref()

const rules = {
    assignedType: [
        { required: true, message: '请选择处理人员', trigger: 'blur' },
    ],
}

const assignedTypeList = [
    {
        label: "指定教职工",
        value: "ASSIGN_USER"
    },
    {
        label: "指定角色",
        value: "ROLE"
    },
    {
        label: "直属主管",
        value: "LEADER"
    },
    {
        label: "连续多级主管",
        value: "LEADER_TOP"
    }
]

const modeList = [
    {
        label: "依此审批（按顺序同意或拒绝）",
        value: "NEXT"
    },
    {
        label: "会签（需要所有审批人都同意才可通过）",
        value: "AND"
    },
    {
        label: "或签（其中一名审批人同意或拒绝即可）",
        value: "OR"
    },

]

const state = reactive({
    stepsList: [],
    current: 0,
    item: {},

    tabsSelection: [
        {
            tab: "教职工组",
            checked: true,
            id: 1,
            key: "staff",
            // 有userId 则是人
            personField: { key: "userId", value: ["userId"] },
            // 单选 true 多选 false
            single: false,
            // 输入框是否显示
            searchOption: {
                show: true,
                displayMode: 'new',
            },
        },
    ],
    tableState: {
        deptId: "", // 部门ID
        status: "", // 状态
        id: "", // 教师id
        name: "",
    },
    toggleTabsType: "people",
    checkUserList: []
})

const title = computed(() => {
    return props.data[state.current]?.name
})
// ------------
const toggleTabs = (item) => {
    // state.toggleTabsType = item.key == 'staff' ? 'people' : 'all';
    state.tableState.name = "";
    state.tableState.accommodation = "";
    // 重置
    // controlRef.value.modelState.loading = false;
    // controlRef.value.modelState.isPpresent = false;
    personRef.value.modelState.dataSource =
        item.key == "staff" ? stateDataSource.value : schoolRollTree.value;
};


function openModelPerson(type) {
    personRef.value.modelState.dataSource = stateDataSource.value
    state.toggleTabsType = type
    if (type == "people") {
        state.tabsSelection = [
            {
                tab: "教职工组",
                checked: true,
                id: 1,
                key: "staff",
                // 有userId 则是人
                personField: { key: "userId", value: ["userId"] },
                // 单选 true 多选 false
                single: false,
                // 输入框是否显示
                searchOption: {
                    show: true,
                    displayMode: 'new',
                },
            },
        ]
    } else {
        state.tabsSelection = [
            {
                tab: "角色组",
                checked: true,
                id: 1,
                key: "role",
                // 单选 true 多选 false
                single: true,
                // 输入框是否显示
                searchOption: {
                    show: true,
                    displayMode: 'new',
                },
            },
        ]
    }




    personRef.value.modelState.open = true
}

// 获取教职工人员列表
const getStaffPages = (callback) => {
    const { pageNo, pageSize, total } = personRef.value.modelState.searchTable;
    // const { roll_status_yes_id = [], emp_status_yes_id = [] } =
    //     store.state.selectSource.dictionary;    
    const { name, accommodation, deptId, } = state.tableState;
    const codes = route.query.codes;
    let params = {
        id: deptId,
        name,
        accommodation,
        code: codes,
        pageNo,
        pageSize,
        total,
    };

    // toggleTabsKey ="staff"
    // 教职工
    params = {
        ...params,
        ...state.tableState,
    };

    http.post('/cloud/employee/page', params)
        .then(({ data }) => {
            let { list } = data;
            // 外部人员 自定义组特需处理
            if (["custom", "outsiders"].includes(state.toggleTabsKey)) {
                list = data.page.list;
            }
            // 学生组
            if (state.toggleTabsKey == "student") {
                list = data?.studentPageListVO?.list || [];
            }
            callback(list);
        })
        .finally(() => {
            personRef.value.modelState.loading = false
        });
};

const toggleLevel = (tabId, item = {}, options) => {
    const { index, trigger } = options;
    // 清空输入框
    state.tableState.name = "";
    state.tableState.accommodation = "";


    // 面包屑
    if (!index) {
        // 第一层数据，恢复原本数据
        personRef.value.modelState.dataSource = state.treeData;
    } else {
        state.tableState.deptId = item.id;
        state.tableState._type = item.type;
        const callback = (data) => {
            let children = item.children || [];
            personRef.value.modelState.dataSource = children?.concat(
                data || []
            );
        };
        getStaffPages(callback);
    }
};

function searchSelect(tabId, item) {
    const { name, pageNo, pageSize } = item
    if (name) {
        // 清空搜索输入框
        state.tableState.name = ''
        // 清空搜索查询list数据
        personRef.value.modelState.searchTable.list = []
        // 选人组件 - 首次聚焦教职工
        state.tableState.name = name
        personRef.value.modelState.loading = true
        const callback = (data) => {
            personRef.value.modelState.searchTable.list = personRef.value.modelState.searchTable.list?.concat(data)
        }
        getStaffPages(callback)
    } else {
        // name为空时，不发送请求，恢复最原始数据
        // !tabId ? setDataSource(state.staffList) : setDataSource(state.studentList)
    }
}

const submitPerson = (e) => {
    console.log(e)
    // formDrawer.assigneeIds = e.map(item => item.id)
    const list = e.map(item => {
        return {
            id: item.id,
            name: item.name
        }
    })
    if (state.toggleTabsType == 'people') {
        state.item.assignedUser = list
    } else {
        state.item.role = list
    }
}

// ------------

watch(() => state.current, (val) => {
    state.stepsList.forEach((item, j) => {
        if (j == val) {
            item.status = 'process'
            state.item.assignedType = item.assignedType
            state.item.mode = item.mode
            state.item.nodeStatus = item.nodeStatus
            state.item.id = item.id
            state.item.level = item.level
            state.item.endCondition = item.endCondition
            state.item.endLevel = item.endLevel
            state.item.assignedUser = item.assignedUser
            state.item.role = item.role

        } else {
            item.status = 'wait'
        }
    })
}, {
    immediate: true
})

watch(() => props.data, (data) => {
    // console.log(data)
    state.stepsList = data.map((item, idx) => {
        return {
            status: state.current == idx ? 'process' : 'wait',
            id: item.id,
            assignedType: item.props.assignedType,
            mode: item.props.mode,
            nodeStatus: item.nodeStatus,
            title: item.name,
            level: item.props.leader?.level,
            endCondition: item.props?.leaderTop?.endCondition,
            endLevel: item.props?.leaderTop?.endLevel,

            assignedUser: item.props?.assignedUser,
            role: item.props?.role,
        }
    })

    state.item.id = props.data[state.current]?.id
}, {
    deep: true,
    immediate: true
})

watch(() => state.item, (item) => {
    emit('update',item)
}, {
    deep: true
})

onMounted(() => {
    http.get('/cloud/app/dept/list', { code: 'cloud' }).then(res => {
        // console.log('人员', res)
        // personRef.value.modelState.dataSource = res.data

        stateDataSource.value = res.data
    })

    http.get('/cloud/roleType/list', { code: 'cloud' }).then(res => {
        // console.log('角色', res)
        // personRef.value.modelState.dataSource = res.data
        schoolRollTree.value = res.data
    })

})

</script>

<style lang="less" scoped>
.flow-setting {
    position: relative;
    height: calc(100vh - 68px);

    // background-color: #fff;
    .flow-steps {
        width: 90%;
        margin: 0 auto;
        margin-top: 16px;
    }

    .flow-from {
        width: 794px;
        margin: 0 auto;
        background-color: #fff;
        padding: 40px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
        margin-top: 16px;
        min-height: 425px;
    }
}
</style>