<template>
    <div class="card-warp">
        <a-row :gutter="[16,24]">
            <a-col v-for="(item, idx) in list" :key="idx" @click="open(item,idx)" :xs="12" :sm="12" :md="8" :lg="8" :xl="6" :xxl="3" :title="item.remark">
               <div class="item">
                    <img class="img" src="@/assets/images/docs.png" alt="">
                    <span class="text">{{item.formName}}</span>
               </div>
            </a-col>
        </a-row>
    </div>
</template>

<script setup>
const emit = defineEmits(['onClick'])

defineProps({
    list: Array,
    default:()=>[]
})

function open(item,idx) {
    emit('onClick', item,idx)
}

</script>

<style lang="less" scoped>
.card-warp {
    padding: 16px;
}
.item {
        // background-color: #f7f8fa;
        border: 1px solid #f6f3f3;
        cursor: pointer;
        border-radius: 5px;
        // box-shadow: 1px 1px 1px #eee;
        overflow: hidden;
        text-align: center;

        padding: 5px;
        box-sizing: border-box;
        user-select: none;
        transition: all .3s ease-in-out;
        position: relative;

        &:hover {
            box-shadow: #999 0 0 5px;
            transition: all .3s ease-in-out;
            background-color: #f7f8fa;
        }
        &:active {
            transform: scale(0.92);            
        }
        &::after{
            content: '';
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .img {
            display: block;
            margin: 0 auto;
        }

        .text {
            display: block;
            padding: 10px;
            box-sizing: border-box;
            width: 165px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
            line-height: 16px;
            margin: auto;
        }
    }
</style>