
<template>
    <div class="box">
        <template v-if="state.list.length">
            <CardList :list="state.list" @onClick="handle"/>
        </template>
        <view v-else class="no-data">            
            <a-empty :imageStyle="{height: '160px',}">
                <template #image>
                    <img src="@/assets/images/empty.png" alt="">
                </template>
            </a-empty>
        </view>
    </div>
    
</template>

<script setup>
    import { onMounted,reactive } from 'vue';
    import { useRouter,useRoute } from 'vue-router';
    import CardList from '../components/cardList.vue';
     import {obfuscate} from '@/utils/common.js';
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
        list:[
            // {
            //     type:"memoDrafts",
            //     title:"签报拟稿-默认模版",                
            // }
        ],
        loading:false,
    })

    function handle(item,idx){
        // console.log(item,idx)
        router.push({
            path:"/draft-operate",
            query:{
                code: obfuscate(JSON.stringify({
                    type:item.type,
                    source:route.name,
                    handle:'create',
                    tempId:item.id,
                })),
            }
        })
    }

    function init(){
        // state.loading = true;
        // state.list.push( ...Object.keys([...new Array(20)]));
        // setTimeout(() => {
        //     state.loading = false;
        // })
        state.loading = true;
         http.post("/cloud/official-doc/process-template/list",{
            type:"memoDrafts",
        }).then(res=>{
            state.list = res.data;
        }).catch(err=>{
            
        }).finally(() => {
            state.loading = false;
        });
    }
    const handleScroll = (e) => {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        const windowHeight = window.innerHeight;
        const scrollHeight = document.documentElement.scrollHeight;
        
        // 距离底部100px时加载
        if (scrollTop + windowHeight >= scrollHeight - 120 && !state.loading) {
            init();
            console.log('加载更多数据')
        }
    };

    onMounted(()=>{
        init()        
        window.addEventListener('scroll', handleScroll);
    })
</script>

<style lang="less" scoped>
    .box{
        position: relative;
        min-height: calc(100vh - 100px);
        .no-data{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);            
        }
    }
</style>