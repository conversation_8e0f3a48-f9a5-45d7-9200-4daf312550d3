<template>
    <div class="log">
        <div class="content">            
            <a-timeline>
                <a-timeline-item v-for="(item,index) in list" :key="index">
                    <div>
                        <span style="font-size: 18px;">{{ item.name }}</span>
                        <span style="padding-left: 20px;" v-if="item.outcomeCode">{{ item.outcomeCode }}</span>
                    </div>
                    <ul style="display: flex;">
                        <li v-for="(user,j) in item.userInfoList" :key="j" style="padding: 10px 0;display: flex;flex-direction: column;align-items: center;">
                            <a-avatar :src="user.avatar" /> 
                            <span style="font-size: 12px;padding-left: 8px;padding-top: 8px;">{{ user.name }}</span>                            
                        </li>
                    </ul>
                </a-timeline-item>
                
            </a-timeline>

             <div class="no-data" v-if="list.length==0">                
                <img class="img" src="@/assets/images/empty.png" alt="">   
                <div class="title">暂无数据</div>             
            </div>
            
        </div>
    </div>
</template>

<script setup>

import { onMounted, reactive } from 'vue';

const props = defineProps({
    // 列表
    list: {
        type: Array,
        default: ()=>[]
    },
})
</script>

<style lang="less" scoped>
.log {
    position: relative;
    width: 800px;
    margin: 0 auto;
    min-height: 100px;
    background-color: #fff;
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075) !important;

    .content {
        padding: 40px !important;
    }

    .no-data{
        text-align: center;
        img{
            width: 200px;
            height: 200px;
            margin-bottom: 20px;
        }
    }
}
</style>