<template>
    <div class="docReceipts">
        <a-form ref="formRef" :model="value" :rules="rules">
            <ul class="item">
                <li class="label" style="width: 25%;">拟稿日期</li>
                <li style="flex:1">
                    <a-date-picker :disabled="disabled" style="height:100%" v-model:value="value.draftDate"
                        :bordered="false" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD">
                        <template #suffixIcon />
                    </a-date-picker>
                </li>
                <li class="label" style="width: 25%;">签报字号</li>
                <li style="flex:1;display: flex;align-items: center;">
                    <a-form-item name="docNo" style="height: 100%;" :label-col="{ style: { width: 0 } }">
                        <a-input :disabled="disabled" v-model:value="value.docNo" :bordered="false" placeholder="请输入" />
                    </a-form-item>
                </li>
            </ul>
            <ul class="item">
                <li class="label" style="width: 25%">签报部门</li>
                <li style="flex:1;display: flex;align-items: center;">
                    <a-form-item name="issuingOrg" style="height: 100%;">
                        <a-input :disabled="disabled" v-model:value="value.issuingOrg" placeholder="请输入"
                            :bordered="false" />
                    </a-form-item>
                </li>
                <li class="label" style="width: 25%">签报类型</li>
                <li style="flex:1;display: flex;align-items: center;">
                    <a-form-item name="fileType" style="height: 100%;flex: 1;width: 100%">
                        <a-select :disabled="disabled" v-model:value="value.fileType" style="width: 100%" allowClear
                            placeholder="请选择" :bordered="false" :options="store.signoffOptions">
                        </a-select>
                    </a-form-item>
                </li>
            </ul>
            <ul class="item">
                <li class="label" style="width: 25%">标题</li>
                <li style="flex:1;display: flex;align-items: center;">
                    <a-form-item name="title">
                        <a-input :disabled="disabled" v-model:value="value.title" placeholder="请输入" :bordered="false"
                            style="width: 100%;" />
                    </a-form-item>
                </li>
            </ul>
            <ul class="item height-100">
                <li class="label" style="width: 25%">内容摘要</li>
                <li style="flex:1">
                    <a-form-item name="content" style="height: 100%;">
                        <a-textarea :disabled="disabled" :bordered="false" v-model:value="value.content" placeholder=""
                            :auto-size="{ minRows: 18, maxRows: 18 }" />
                    </a-form-item>
                </li>
            </ul>
            <ul class="item">
                <li class="label" style="width: 25%;">签发</li>
                <li style="flex:1;display: flex;align-items: center;overflow: hidden;">
                    <a-radio-group :disabled="disabled" v-model:value="value.isIssued" name="radioGroup"
                        style="display: flex; justify-content: center;">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                </li>
                <li class="label" style="width: 25%;">承办</li>
                <li style="flex:1;display: flex;align-items: center;overflow: hidden;">
                    <a-radio-group :disabled="disabled" v-model:value="value.isHandled" name="radioGroup"
                        style="display: flex; justify-content: center;">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                </li>
            </ul>
        </a-form>
    </div>
</template>

<script setup>

import { onMounted, reactive, ref } from 'vue';
import { debounce } from "@/utils/common";
import { message } from 'ant-design-vue';
const formRef = ref(null)
const store = useStore()
const emit = defineEmits(['validate'])

const props = defineProps({
    value: {
        type: Object,
        default: () => ({})
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const rules = {
    docNo: [
        {
            required: true,
            message: '请输入签报字号',
            trigger: 'blur'
        },
        { max: 20, message: '最多只能输入20个字符', trigger: 'blur' },
    ],
    issuingOrg: [
        {
            required: true,
            message: '请输入签报部门',
            trigger: 'blur'
        },
        { max: 20, message: '最多只能输入20个字符', trigger: 'blur' },
    ],
    fileType: [
        {
            required: true,
            message: '请选择签报类型',
            trigger: 'change'
        },
    ],
    title: [
        {
            required: true,
            message: '请输入标题',
            trigger: 'blur'
        },
        { max: 50, message: '最多只能输入50个字符', trigger: 'blur' },
    ],
    content: [
        { max: 200, message: '最多只能输入200个字符', trigger: 'blur' },
    ]
}

const state = reactive({

})

function filterSizeOption(input, option) {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}

const msgTips = debounce((msg)=>{
    message.error(msg)
}, 500)


function validate(cb){
    formRef.value.validate().then(res=>{
        cb && cb(res)
    }).catch(err=>{        
        const error = err.errorFields[0]
        const msg = error.errors[0]
        msg && msgTips(msg)
    })
}

onMounted(() => {
    emit('validate', validate)
})
</script>

<style lang="less" scoped>
@border-color: #d0021b;
@rowHeight: 92px;
@font-family: STFangsong, FangSong, 华文仿宋, 仿宋, serif;
@font-size: 16px;

.docReceipts {
    position: relative;
    border: 1px solid @border-color;
    font-family: @font-family;

    .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid @border-color;

        &:first-child {
            border-top: none !important;
        }

        li {
            box-sizing: border-box;
            text-align: center;
            height: @rowHeight;
            line-height: @rowHeight;

            &:not(:last-child) {
                border-right: 1px solid @border-color;
            }

        }

        .label {
            font-weight: 600;
            color: #000;
            font-size: @font-size;
            font-family: @font-family;
        }
    }

    .height-100 {
        li {
            height: 500px;
            line-height: 500px;
        }
    }

    :deep(.ant-radio-wrapper) {
        display: flex;
        justify-content: center;
    }

    :deep(.ant-picker-input) {
        input {
            font-size: @font-size;
            font-family: @font-family;
        }
    }

    :deep(.ant-select-selection-placeholder) {
        font-size: @font-size;
        font-family: @font-family;
    }

    :deep(.ant-input) {
        font-size: @font-size;
        font-family: @font-family;
    }

    :deep(.ant-select-selection-item) {
        font-size: @font-size;
        font-family: @font-family;
    }

    :deep(.ant-select-selector) {
        height: 100% !important;
    }

    :deep(.ant-form-item) {
        width: 100%;
    }

    :deep(.ant-form-item-explain-error) {
        text-align: left;
        padding-left: 11px;
    }

}
</style>
