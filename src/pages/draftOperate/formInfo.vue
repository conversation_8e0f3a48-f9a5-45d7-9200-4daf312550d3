<template>
    <div class="form-info__warp">
        <div class="form-info">
            <div class="body">
                <div class="preview-box">
                    <div class="print-preview">
                        <div class="preview__mask"></div>
                        <div class="preview__modal" style="width: 210mm;">
                            <div id="printBox" class="preview__modal__scroll">
                                <Preview :options="options"></Preview>

                                <!-- <div class="excel-content" style="width: 210mm;height: 297mm;padding: 20mm 17mm;">
                                    <div class="runtime-layout">
                                        <div class="preview__page-corner lb"></div>
                                        <div class="preview__page-corner lt"></div>
                                        <div class="preview__page-corner rb"></div>
                                        <div class="preview__page-corner rt"></div>
                                        <div class="runtime-page-header"></div>
                                        <div class="runtime-page-footer"></div>
                                        <div class="table-body">                                           
                                            <DocDrafts :disabled="disabled" @validate="(e)=>$emit('validate',e)" v-model:value="state.form" v-if="temp == 'docDrafts'" />
                                            <DocReceipts :disabled="disabled" @validate="(e)=>$emit('validate',e)" v-model:value="state.form" v-if="temp=='docReceipts'"/>
                                            <Signoff :disabled="disabled" @validate="(e)=>$emit('validate',e)" v-model:value="state.form" v-if="temp=='memoDrafts'"/> 
                                        </div>
                                    </div>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script setup>
import { watch, reactive,ref,onMounted } from 'vue';
import Preview from "@/pages/templateOperate/components/manuscriptDesign/preview.vue";

import DocDrafts from "./template/docDrafts.vue"
import DocReceipts from "./template/docReceipts.vue"
import Signoff from "./template/signoff.vue"
const formRef = ref(null)

const props = defineProps({
    // 模版
    temp: {
        type: String,
        default: ''
    },
    // 回填数据
    data: {
        type: Object,
        default: () => {
            return {}
        }
    },
    disabled: {
        type: Boolean,
        default: false
    },
    options: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update'])
const state = reactive({
    form: {
        draftDate: '',
        docNo: null,
        issuingOrg: '',
        title: '',
        urgencyLevel: null,
        securityClassification: null,
        fileType: null,
        content: '',
        isReviewed: 1, // 核稿
        isIssued: 1, // 签发
        isDraftPrepare: 1, // 是否拟办
        isApprovalHandled: 1, // 是否批办
        isHandled: 1, // 是否承办
    }
})

watch(() => state.form, (newVal, oldVal) => {
    emit('update', newVal)
}, { deep: true, immediate: true })


watch(() => props.data, (newVal, oldVal) => {
    for(let k in state.form){
        if(['isReviewed', 'isIssued', 'isDraftPrepare', 'isApprovalHandled', 'isHandled'].includes(k)){
            state.form[k] = newVal[k] == true ? 1 : 0;
            continue
        }
       state.form[k] = newVal[k]
    }
}, { deep: true, })


</script>

<style lang="less" scoped>
.form-info {
    position: relative;


    .body {
        width: 854px;
        display: block;
        // min-height: 1123px;
        margin: 0 auto 50px auto;
        box-sizing: border-box;
        position: relative;
        background: rgb(255, 255, 255);
        padding: 40px !important;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }
}

.preview-box {
    width: 100%;
    // height: 100vh;
    box-sizing: border-box;

    .print-preview {
        width: 100%;
        height: 100%;
        position: relative;

        .preview__mask {
            // position: fixed;
            // left: 0;
            // top: 0;
            // right: 0;
            // bottom: 0;
            // z-index: 999;
            // background-color: rgba(15, 28, 53, .25);
        }

        .preview__mask__scroll {
            overflow-y: auto;
            overflow-x: hidden;
            width: 100%;
            height: 100%;           
        }       
    }
}
</style>