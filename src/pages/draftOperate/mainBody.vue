<template>
    <div class="main-body">
        <div style="margin:0 auto 16px auto;width: 794px;" v-if="previewUrl && !disabled">
            <a-upload class="btn" v-model:file-list="state.fileList" name="file" :action="state.action"
                :headers="state.headers" @change="handleChange" :before-upload="beforeUpload" :maxCount="1">
                <a-button :disabled="state.loading">
                    <upload-outlined></upload-outlined>
                    上传原文
                </a-button>
            </a-upload>
        </div>
        <div class="content">
            <template v-if="previewUrl">
                <a-spin tip="Loading..." :spinning="state.iframeLoading">
                    <iframe class="iframe" :src="previewUrl"
                        title="333" width="793px" height="1122px" allowfullscreen loading="lazy" frameborder="0"
                        sandbox="allow-scripts allow-same-origin" referrerpolicy="no-referrer-when-downgrade"
                        tabindex="0" @error="iframeError" @load="iframeLoad"></iframe>
                </a-spin>
            </template>
            <div class="no-data" v-else>
                <div class="title" v-if="!disabled">{{ state.loading ? '正在上传...' : '请上传正文内容' }}</div>
                <img class="img" src="@/assets/images/empty.png" alt="">
                <div class="desc" v-if="!disabled">
                    支持DOC、PDF、Excel、PNG、JPG格式文件，最多上传一个，再次上传会替代原文内容。
                </div>
                <div class="desc" v-else>
                    未上传正文内容
                </div>
                <a-upload :key="state.updateKey" v-if="!disabled" class="btn" v-model:file-list="state.fileList" name="file"
                    :action="state.action" :headers="state.headers" @change="handleChange" :before-upload="beforeUpload">
                    <a-button :disabled="state.loading" :maxCount="1">
                        <upload-outlined></upload-outlined>
                        点击上传
                    </a-button>
                </a-upload>
            </div>
        </div>
    </div>
</template>

<script setup>

import { onMounted, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import http from '@/utils/http';

const emit = defineEmits(['update'])

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const store = useStore();

const state = reactive({
    fileList: [],
    headers: {
        Authorization: 'Bearer ' + store.token
    },
    action: `${import.meta.env.VITE_BASE_API}/cloud/official-doc/file/upload`,

    loading: false,

    fileUrl: '',
    updateKey:1,
    iframeLoading:true
})

const previewUrl = computed(() => {
    if (state.fileUrl) {
        // return  encodeURIComponent(Base64.encode(state.fileUrl))
       return `${import.meta.env.VITE_BASE_PREVIEW}?url=${encodeURIComponent(Base64.encode(state.fileUrl))}`
    }
})

watch(() => props.data, (newVal, oldVal) => {
    if (newVal.fileUrl) {
        state.fileUrl = newVal.fileUrl
        emit('update', {
            fileUrl: newVal.fileUrl,
            fileId: newVal.fileId
        })
    }
}, { deep: true, immediate: true })


function iframeLoad(){
    console.log('iframeLoad')
    state.iframeLoading = false
}

function iframeError(){
    state.iframeLoading = false
    message.error('预览加载失败')
}


function beforeUpload(file,fileList){
    const isLt2M = file.size / 1024 / 1024 < 50;
    if (!isLt2M) {
        message.error('正文最大限制50M，已超过最大限制!');
    }
    return isLt2M;
}

function handleChange(info) {
    console.log(info.file.status);
    if (info.file.status == 'uploading') {        
        state.loading = true;
        return;
    }
    // debugger
    if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
        state.loading = false;
    }
    if (info.file.status === 'done') {
        if (info.file.response.code == 0) {
            emit('update', {
                fileUrl: info.file.response.data.url,
                fileId: info.file.response.data.id
            })
            state.iframeLoading = true
            state.fileUrl = info.file.response.data.url
            message.success(info.file.response.message);
        } else {
            message.error(info.file.response.message);
            
            state.fileList = [];
        }
        state.loading = false;
    } else if (info.file.status === 'error') {
        state.loading = false;
        message.error(info.file.response.message);
    }
}

function init() {

}

onMounted(() => {
    init()
})
</script>

<style lang="less" scoped>
.main-body {
    position: relative;
    padding: 0 16px;

    .content {
        position: relative;
        width: 794px;
        // min-height: 1123px;
        margin: auto;
        // padding: 40px !important;
        box-sizing: border-box;
        position: relative;
        background: #fff;
        box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075) !important;
    }

    .no-data {
        text-align: center;
        padding: 40px 0;

        .title {
            font-size: 22px;
        }

        .img {
            width: 194px;
            height: auto;
            margin-top: 16px;
        }

        .desc {
            padding: 10px 10px 24px 10px;
        }

        .btn {
            width: max-content;
            margin: 0 auto;
            display: inline-block;
        }
    }
}
</style>