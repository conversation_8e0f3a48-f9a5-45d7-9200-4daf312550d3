<template>
    <div class="draft-container" style="overflow-y: auto;">
        <a-tabs class="tabs" v-model:activeKey="state.activeKey" centered>
            <template #leftExtra>
                <a-button type="link" class="back-btn" @click="$router.back()">
                    <template #icon>
                        <LeftOutlined />
                    </template>
                    返回
                </a-button>
                <span :key="state.title">{{ state.title }}</span>
            </template>
            <template #rightExtra>
                <div style="display: flex;">
                    <div style="width: 60px;height: 1px;"></div>                    
                    <!--  -->
                     <a-button @click="openFallback" v-if="isFallback">回退</a-button>                     
                     
                    <template v-if="isCreate ||  (isNiGgo && !isLook) || (isDengJi && !isLook)">
                        <a-button mr-15 type="primary" @click="submit" :loading="state.getNextLogin">提交</a-button>
                    </template>                   
                    <template v-if="isAgree && !isLook">
                        <a-button mr-15 type="primary" @click="submit" :loading="state.getNextLogin">同意</a-button>
                    </template>
                    <template v-if="isCirculate && !isLook">
                        <a-button mr-15 type="primary" @click="submit" :loading="state.getNextLogin">传阅</a-button>
                    </template>
                     <template v-if="isRead && !isLook">
                        <a-button mr-15 type="primary" @click="submit" :loading="state.getNextLogin">阅读</a-button>
                    </template>
                    <template v-if="isOrganize && !isLook">
                        <a-button mr-15 type="primary" @click="submit" :loading="state.getNextLogin">承办</a-button>
                    </template>
                    <template v-if="isCompleted && !isLook">
                        <a-button mr-15 type="primary" @click="submit" :loading="state.getNextLogin">办结</a-button>
                    </template>
                    <template v-if="isSealup && !isLook">
                        <a-button mr-15 type="primary" @click="submit" :loading="state.getNextLogin">归档</a-button>
                    </template>
                    
                </div>
            </template>
            <a-tab-pane key="1" tab="稿纸信息">
                <a-spin tip="Loading..." :spinning="state.spinningTab1">
                    <FormInfo :options="state.excelOptions" :data="state.detail" :disabled="disabled" @update="updateField" :temp="state.query.type" ref="formInfoRef" @validate="validateForm"/>
                </a-spin>
            </a-tab-pane>
            <a-tab-pane key="2" tab="正文">
                <a-spin tip="Loading..." :spinning="state.spinningTab2">
                    <MainBody :disabled="disabled" :data="state.detail" @update="updateField" />
                </a-spin>
            </a-tab-pane>
            <a-tab-pane key="4" tab="附件">

            </a-tab-pane>
            <a-tab-pane key="3" tab="日志" v-if="!isCreate">
                <a-spin tip="Loading..." :spinning="state.spinningTab3">
                    <Log :list="state.logList"/>
                </a-spin>
            </a-tab-pane>
        </a-tabs>
        <!--  -->
        <a-drawer
            v-model:open="state.openDrawer"
            :title="isCreate ? '提交' : state.detail.statusCode + '操作'"
            placement="right"
        >
            <a-form :model="formDrawer" :rules="rules" layout="vertical" ref="formDrawerRef">
                <template v-if="state.isEndNode">
                    <a-form-item label="办理结果：" style="margin-bottom: 20px;">
                        <a-button disabled>归档</a-button>
                    </a-form-item>
                </template>                
                <template v-else>
                    <a-form-item label="办理结果：" v-if="!isNiGgo && !isDengJi && !isCreate" style="margin-bottom: 20px;">
                        <a-button disabled>同意</a-button>
                    </a-form-item>
                    <a-form-item label="下一步操作：" style="margin-bottom: 20px;">
                        <a-button disabled>{{ state.nextFlowTitle }}</a-button>
                    </a-form-item>
                    <a-form-item label="办理人员：" name="assigneeIds" style="margin-bottom: 20px;">
                        <template v-if="state.nextUserList.length">
                            <a-button disabled>                               
                                {{ state.nextUserList.map(item=>item.name).join("，") 
                            }} 
                        </a-button>
                        </template>
                        <a-button v-else @click="openModelPerson">
                            <PlusOutlined />
                        {{ state.checkUserList.map(item=>item.name).join("，") || '添加人员'}} 
                        </a-button>
                    </a-form-item>
                </template>
                <a-form-item label="意见输入：" name="comment" style="margin-bottom: 20px;">
                     <a-textarea
                        v-model:value="formDrawer.comment"
                        :rows="4"
                        show-count 
                        :maxlength="200"
                    />
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button @click="state.openDrawer = false">取消</a-button>
                <a-button v-if="state.isEndNode" type="primary" :loading="state.submitFlowLoading" @click="confirmSealup">确认</a-button>
                <a-button v-else type="primary" :loading="state.submitFlowLoading" @click="submitFlow">
                    确定
                </a-button>                
            </template>
        </a-drawer>
        <!-- 回退 -->
         <a-drawer
            v-model:open="state.fallback.open"
            title="办理操作"
            placement="right"
        >
            <a-form :model="fallbackDrawer" layout="vertical" ref="fallbackRef">
                <a-form-item label="办理结果：" style="margin-bottom: 20px;">
                    <a-button disabled>回退</a-button>
                </a-form-item>
                <a-form-item label="意见输入：" name="comment" style="margin-bottom: 20px;">
                     <a-textarea
                        v-model:value="fallbackDrawer.comment"
                        :rows="4"
                        show-count 
                        :maxlength="200"
                    />
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button @click="state.fallback.open = false">取消</a-button>
                <a-button type="primary" :loading="state.fallback.loading" @click="fallbackTask">
                    确定
                </a-button>
            </template>
        </a-drawer>       
        <!-- 选择人员 -->
        <PersonSelectionControl ref="personRef" type="people" 
            :tabs="state.tabsSelection"  @toggleLevel="toggleLevel"
            @submit="submitPerson"  @search="searchSelect" :maxOptional="20" :selected="state.checkUserList"/>
        <!--  -->
    </div>
</template>

<script setup>
import {ExclamationCircleOutlined,LeftOutlined,PlusOutlined} from '@ant-design/icons-vue'
import { onMounted, reactive, createVNode,ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import FormInfo from "./formInfo.vue"
import MainBody from "./mainBody.vue"
import Log from "./log.vue"
import http from '@/utils/http'
import {deobfuscate} from '@/utils/common.js';

import PersonSelectionControl from "@/components/PersonSelectionControl/index.vue"
const personRef = ref(null)
const formDrawerRef = ref(null)
const formInfoRef = ref(null)

const route = useRoute()
const router = useRouter()
const typeMap = [
    { type: 'docDrafts', title: "发文拟稿" },
    { type: 'docReceipts', title: "收文登记" },
    { type: 'memoDrafts', title: "签报拟稿" }
]

const state = reactive({
    activeKey: '1',
    title: '',
    loadingPaper: false,
    excelOptions:{},
    query:{},
    // 
    form: {

    },
    openDrawer: false,

    tabsSelection:[
        {
            tab: "教职工组",
            checked: true,
            id: 1,
            key: "staff",
            // 有userId 则是人
            personField: { key: "userId", value: ["userId"] },
            // 单选 true 多选 false
            single: false,
            // 输入框是否显示
            searchOption: {
                show: true,
                displayMode: 'new',
            },
        },
    ],
    tableState: {
        deptId: "", // 部门ID
        status: "", // 状态
        id: "", // 教师id
        name: "",
    },
    checkUserList:[],
    nextFlowTitle: "",
    submitFlowLoading:false,

    spinningTab1: false,
    spinningTab2: false,
    spinningTab3: false,

    // 详情
    detail: {},
    logList:[],
    // 回退
    fallback:{
        open:false,
        loading:false,        
    },
    // 是否归档最好一个节点
    isEndNode:false,
    // 下一个节点是否需要选择人员
    nextUserList:[],
    getNextLogin:false
})
// 回退表单
const fallbackDrawer = reactive({
    comment:''
})
// 下一步表单
const formDrawer = reactive({

})

const rules = {
    assigneeIds: [
        { required: true, message: '请选择办理人员', trigger: 'blur' },
    ],
    // comment: [
    //     { required: true, message: '请输入意见', trigger: 'blur' },
    // ],
}



// 是否是创建
const isCreate = computed(() => {
    return state.query.handle == 'create'
})
// 是否是查看
const isLook = computed(() => {
    return state.query.handle == 'view'
})
// 是否是编辑
const isHandle = computed(() => {
    return state.query.handle == 'handle'
})
// 是否拟稿
const isNiGgo = computed(() => {
    return state.detail.status == 'NI_GAO'
})
// 是否登记
const isDengJi = computed(() => {
    return state.detail.status == 'DENG_JI'
})
// 是否同意
const isAgree = computed(() => {
    // QIAN_FA("QIAN_FA", "签发"), HUI_QIAN("HUI_QIAN", "会签"), HE_GAO("HE_GAO", "核稿"),
    // PI_BAN("PI_BAN", "批办"),NI_BAN("NI_BAN", "拟办"),CHU_SHEN("CHU_SHEN", "初审"),
    // QIAN_FA("QIAN_FA", "签发"),HUI_QIAN("HUI_QIAN", "会签"),
    return ["QIAN_FA","HUI_QIAN","HE_GAO","PI_BAN","NI_BAN","CHU_SHEN","QIAN_FA","HUI_QIAN"].includes(state.detail.status)
})
// 是否传阅
const isCirculate = computed(() => {
    return ["CHUAN_YUE"].includes(state.detail.status)
})
// 是否阅读
const isRead = computed(() => {
    return ["YUE_DU"].includes(state.detail.status)
})
// 是否承办
const isOrganize = computed(() => {
    return ["CHENG_BAN"].includes(state.detail.status)
})
// 是否办结
const isCompleted = computed(() => {
    return ["BAN_JIE"].includes(state.detail.status)
})
// 是否归档
const isSealup = computed(() => {
    return ["GUI_DANG"].includes(state.detail.status)
})
// 已归档
const isSealupEnd = computed(() => {
    return ["YI_GUI_DANG"].includes(state.detail.status)
})


// 是否可以回退
const isFallback = computed(() => {
    if(isCreate.value){
        return false
    }
    if(isLook.value){
        return false
    }   
    if(isDengJi.value){
        return false
    }
    if(isSealupEnd.value){
        return false
    }   
    if(isNiGgo.value){
        return false
    }
    return true
})


// 禁用
const disabled = computed(() => {
    let result = true      
    if(isCreate.value){
        result = false
    }    
    if(isNiGgo.value && !isLook.value){
        result = false
    }
     if(isDengJi.value && !isLook.value){
        result = false
    }
    
    return result 
})

// 打开回退
function openFallback() {
   state.fallback.open = true
   fallbackDrawer.comment = ''
}

// 回退操作
function fallbackTask() {    
    state.fallback.loading =  true
    http.post(`/cloud/official-doc/workflow/changeActivityState`,{
        taskId: state.detail.taskId,
        comment: fallbackDrawer.comment
    }).then(()=>{
        message.success("操作成功")
        router.push({
            name:state.query.source,
        })
    }).finally(()=>{
        state.fallback.loading =  false
        state.fallback.open = false        
    })
}

let tempValidate = null

function validateForm(validate){
    tempValidate = validate
}

// 确认归档
function confirmSealup(){
    Modal.confirm({
        title: '确定归档?',
        icon: createVNode(ExclamationCircleOutlined),
        content: "确定要归档么？",
        onOk() {
            submitFlow()
        },
        onCancel() {
            console.log('Cancel');
        },
    });
}

function submitFlow(){
    formDrawerRef.value.validate().then(res=>{
        state.submitFlowLoading = true
        const url = isCreate.value?  `/cloud/official-doc/workflow/startProcInstance` :`/cloud/official-doc/workflow/completeTask`
        let p = {}
        if(isCreate.value){
            p = {...formDrawer}
        }else{
            p.outcome = 2 //0拒绝 2通过
            p.taskId = state.detail.taskId
            p.nextNodeId = formDrawer.nodeId       
            p.assigneeIds =  formDrawer.assigneeIds?.length ? formDrawer.assigneeIds : state.nextUserList.map(item=>item.id) 
            p.comment =  formDrawer.comment
        }
        http.post(url,p).then(res=>{
            // 
            message.success("提交成功")
            router.push({
                name:state.query.source,
            })
        }).finally(()=>{
            state.submitFlowLoading = false
        })
    }).catch(err=>{
        console.log(err)
    })
}

async function sendSubmit(resolve, reject) {
    console.log(state.form)
    const type = state.query.type
    try {
        const { code, data } = await http.get(`/cloud/formProcessTemplates/getByKey`, { formKey: type })
        const formItems = JSON.parse(data.formItems)
        const children = formItems[0]?.children || []
        console.log(children)

        for (const item of children) {
            if (item.attribute == '') continue;
            if (['isReviewed', 'isIssued','isDraftPrepare','isApprovalHandled','isIssued','isHandled'].includes(item.attribute)) {
                item.value = state.form[item.attribute] ? '是' : '否'
                continue
            }
        }
        const p = {
            ...state.form,
            type,
            formJson: JSON.stringify(children)
        }
        console.log(p)
        const url = isCreate.value ?  `/cloud/official-doc/document/create` :`/cloud/official-doc/document/update`
        if(!isCreate.value){
            p.id = state.detail.id
        }
        state.getNextLogin = true
        http.post(url,p).then(res => {
            if (res.code == 0) {
                // message.success("提交成功")
                resolve && resolve()
                const documentId = typeof res.data == 'boolean' ? p.id : res.data
                formDrawer.documentId = documentId 
                // 获取下一个流程
                http.post('/cloud/official-doc/workflow/getNextFlowElement',{documentId}).then(next=>{
                    console.log(next)
                    state.openDrawer = true
                    state.isEndNode = next.data.isEndNode
                    formDrawer.nodeId = next.data.id
                    formDrawer.nodeCode = next.data.code
                    state.nextFlowTitle = next.data.name
                    state.nextUserList = next.data.userInfoList || []
                    if(state.nextUserList?.length){
                        formDrawer.assigneeIds = next.data.userInfoList.map(item=>item.id)
                    }
                }).finally(()=>{
                    
                })
            } else {
                message.error(res.message)
               reject && reject()
            }
        }).catch(err => {
            reject && reject()
        }).finally(()=>{
            state.getNextLogin = false
        })
    } catch (error) {
       reject &&  reject(error)
    }
}

function openModelPerson(){
    personRef.value.modelState.open = true
}

function submit() {
    tempValidate && tempValidate((res)=>{
        if((isCreate.value && !state.form.fileUrl) || (isNiGgo.value &&!state.form.fileUrl)){
            Modal.confirm({
                title: '操作提醒',
                icon: createVNode(ExclamationCircleOutlined),
                content: '当前公文没有上传正文，确认要提交吗？',
                async onOk() {
                    return await new Promise((resolve, reject) => {
                        sendSubmit(resolve, reject);
                        // 获取人员部门
                        http.get('/cloud/app/dept/list',{code:'cloud'}).then(res=>{
                            personRef.value.modelState.dataSource = res.data                
                        })
                    });
                },
                onCancel() { },
            });
        }else{
            sendSubmit();
                // 获取人员部门
            http.get('/cloud/app/dept/list',{code:'cloud'}).then(res=>{
                personRef.value.modelState.dataSource = res.data                
            })
        }
    })
}


function updateField(data) {
    state.form = {
        ...state.form,
        ...data
    }
    // console.log(state.form)
}

function init() {
    const query = deobfuscate(route.query.code)
    // console.log(query)
    if(query.errorMsg){
        message.error("参数异常，请重新打开页面")
        setTimeout(() => {
            router.back()
        }, 1500)
    }else{
        state.query = query
        const item = typeMap.find(item => item.type === query.type)
        if (item) {
            state.title = item.title
        }
        if(query.tempId){
            http.get("/cloud/official-doc/process-template/get",{id:query.tempId}).then(res=>{
                try {
                    const settings = JSON.parse(res.data.settings)
                    console.log('模板详情',settings)
                    state.excelOptions = settings
                } catch (error) {
                    
                }
            })
            
        }


        if(query.id){
            // 查询公文详情
            state.spinningTab1 = true
            http.get('/cloud/official-doc/document/get',{id:query.id}).then(res=>{
                console.log('公文详情',res.data)

                const { statusCode,procInstId } = res.data
                state.title = statusCode
                state.detail = res.data
                state.form.fileUrl = res.data.fileUrl
                state.form.fileId =  res.data.fileId

                // 获取办理日志
                state.spinningTab3 = true
                http.get('/cloud/official-doc/workflow/getProcFlowElementList',{procInstId}).then(res=>{
                    // console.log('日志',res.data)
                    state.logList = res.data
                }).finally(()=>{
                    state.spinningTab3 = false
                })
            }).finally(()=>{
                state.spinningTab1 = false
            })            
        }
    }
}

// -------




const submitPerson = (e)=>{
    console.log(e)
    formDrawer.assigneeIds = e.map(item=>item.id)

    state.checkUserList = e
}


// 获取教职工人员列表
const getStaffPages = (callback) => {
    const { pageNo, pageSize, total } = personRef.value.modelState.searchTable;
    // const { roll_status_yes_id = [], emp_status_yes_id = [] } =
    //     store.state.selectSource.dictionary;    
    const { name, accommodation, deptId, } = state.tableState;
    const codes = route.query.codes;
    let params = {
        id: deptId,
        name,
        accommodation,
        code: codes,
        pageNo,
        pageSize,
        total,
    };
    
        // toggleTabsKey ="staff"
        // 教职工
        params = {
            ...params,
            ...state.tableState,            
        };
    
    http.post('/cloud/employee/page',params)
        .then(({ data }) => {
            let { list } = data;
            // 外部人员 自定义组特需处理
            if (["custom", "outsiders"].includes(state.toggleTabsKey)) {
                list = data.page.list;
            }
            // 学生组
            if (state.toggleTabsKey == "student") {
                list = data?.studentPageListVO?.list || [];
            }
            callback(list);
        })
        .finally(() => {
            personRef.value.modelState.loading = false
        });
};

const toggleLevel = (tabId, item = {}, options) => {
    const { index, trigger } = options;
    // 清空输入框
    state.tableState.name = "";
    state.tableState.accommodation = "";
    
   
    // 面包屑
    if (!index) {
        // 第一层数据，恢复原本数据
        personRef.value.modelState.dataSource = state.treeData;
    } else {
        state.tableState.deptId = item.id;
        state.tableState._type = item.type;
        const callback = (data) => {
            let children = item.children || [];
            personRef.value.modelState.dataSource = children?.concat(
                data || []
            );
        };
        getStaffPages(callback);
    }
};

function searchSelect(tabId, item) {
    const { name, pageNo, pageSize } = item
    if (name) {
        // 清空搜索输入框
        state.tableState.name = ''
        // 清空搜索查询list数据
        personRef.value.modelState.searchTable.list = []
        // 选人组件 - 首次聚焦教职工
        state.tableState.name = name
        personRef.value.modelState.loading = true
        const callback = (data) => {
            personRef.value.modelState.searchTable.list = personRef.value.modelState.searchTable.list?.concat(data)
        }
        getStaffPages(callback)
    } else {
        // name为空时，不发送请求，恢复最原始数据
        // !tabId ? setDataSource(state.staffList) : setDataSource(state.studentList)
    }
}
// --------

onMounted(() => {
    init()
})
</script>

<style lang="less" scoped>
.back-btn {
    color: #333;

    &:hover {
        color: #00b781;
    }
}

.draft-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 600;


    min-height: 100%;
    background-color: #f7f8fa;

    .tabs {
        min-height: 100%;

        :deep(.ant-tabs-nav) {
            margin-bottom: 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            height: 48px;
        }

        :deep(.ant-tabs-content-holder) {
            margin-top: 64px;
        }

        :deep(.ant-tabs-nav) {
            background-color: #fff;
        }
    }
}
</style>