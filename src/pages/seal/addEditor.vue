<template>
  <a-modal
    :open="open"
    :title="title"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="state.submitLoading"
    :okButtonProps="{ disabled: state.loadLoading }"
  >
    <div class="container">
      <a-spin :spinning="state.loadLoading">
        <a-form
          :model="formState"
          ref="formRef"
          :rules="rules"
          layout="vertical"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 24 }"
          style="padding: 0 16px"
        >
          <a-form-item
            label="印章名称："
            name="sealName"
            style="margin-bottom: 14px"
          >
            <a-input
              v-model:value="formState.sealName"
              placeholder="请输入"
              :maxlength="20"
              show-count
            />
          </a-form-item>
          <a-form-item
            label="印章："
            name="sealUrl"
            style="margin-bottom: 14px"
          >
            <a-upload
              v-model:file-list="state.fileList"
              name="file"
              list-type="picture-card"
              class="avatar-uploader"
              :show-upload-list="false"
              :action="state.action"
              :before-upload="beforeUpload"
              @change="handleChange"
              :headers="state.headers"
            >
              <img
                v-if="formState.sealUrl"
                :src="formState.sealUrl"
                alt="avatar"
                style="height: 100px; width: 100px; object-fit: contain"
              />
              <div v-else>
                <loading-outlined v-if="state.loading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div class="ant-upload-text">Upload</div>
              </div>
            </a-upload>
            <p style="color: #666">
              仅支持上传jpeg/jpg/png格式文件，单个文件不能超过1M,单次最多可选1张
            </p>
          </a-form-item>
        </a-form>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
import { toRaw, watch } from "vue";
import { message } from "ant-design-vue";
import { PlusOutlined, LoadingOutlined } from "@ant-design/icons-vue";

const formRef = ref();
const emit = defineEmits(["update:open", "submit"]);

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增",
  },
  mode: {
    type: String,
    default: "add",
    validator: (val) => ["add", "edit", "look"].includes(val),
  },
  getRead: Function,
  id: String,
});

// 表单校验
const rules = {
  sealName: [{ required: true, message: "请输入名称" }],
  sealUrl: [{ required: true, message: "请上传印章" }],
};

const store = useStore();
const state = reactive({
  submitLoading: false,
  loadLoading: false,
  loading: false,
  fileList: [],
  headers: {
    Authorization: "Bearer " + store.token,
  },
  action: import.meta.env.VITE_BASE_API + `/file/cloud/common/upload`,
});

const formState = reactive({
  sealName: "",
  sealUrl: "",
});

const resetForm = () => {
  state.submitLoading = false;
  state.loadLoading = false;
  formRef.value.resetFields();
};

function getBase64(img, callback) {
  const reader = new FileReader();
  reader.addEventListener("load", () => callback(reader.result));
  reader.readAsDataURL(img);
}

const handleChange = (info) => {
  state.loading = true;
  getBase64(info.file, (base64Url) => {
    formState.sealUrl = base64Url;
    state.loading = false;
  });
  return;
  if (info.file.status === "uploading") {
    state.loading = true;
    return;
  }
  if (info.file.status === "done") {
    // if (info.file.response.code == 0) {
    //     formState.sealUrl = info.file.response.data.url
    //     message.success(info.file.response.message);
    // } else {
    //     message.error(info.file.response.message);
    //     state.fileList = [];
    // }
    // state.loading = false;
  }
  if (info.file.status === "error") {
    state.loading = false;
    message.error("上传失败");
  }
};

const beforeUpload = (file) => {
  const isLt2M = file.size / 1024 / 1024 < 1;
  if (!isLt2M) {
    message.error("图像必须小于1MB！");
  }
  return false;
};

const validate = (cb) => {
  formRef.value
    .validate()
    .then(() => {
      cb && cb(toRaw(formState));
    })
    .catch((error) => {
      console.log("error", error);
    });
};

watch(
  () => props.open,
  (v) => {
    if (v && props.mode == "edit") {
      if (props.getRead) {
        state.loadLoading = true;
        props
          .getRead({ id: props.id })
          ?.then((res) => {
            console.log(res);
            const data = res.data || {};
            if (Object.keys(data).length && props.mode == "edit") {
              for (let k in formState) {
                formState[k] = data[k];
              }
            }
            state.loadLoading = false;
          })
          .catch((err) => {
            // message.error(err?.message || err?.msg)
            state.loadLoading = false;
          });
      }
    }
    if (v && props.mode == "add") {
      state.loadLoading = false;
    }
  },
  {
    important: true,
  }
);

function handleOk() {
  validate(async (data) => {
    state.submitLoading = true;
    try {
      if(state.fileList.length){
        const res = await http.form('/file/cloud/common/upload',{
            file:state.fileList[0]?.originFileObj,
            folderType:`enrollmentReport`
        })
        data.sealUrl = res.data[0]?.url
      }
        emit("submit", {
            data: data,
            mode: props.mode,
            complete: (boole) => {
                if (!boole) {
                state.submitLoading = false;
                return;
                }
                handleCancel();
            },
        });
    } catch (error) {
        message.error(error?.message || error?.msg)
         state.submitLoading = false;
    }
  });
}

function handleCancel() {
  emit("update:open", false);
  resetForm();
  if (state.loadLoading) {
    // 取消请求
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  padding: 16px 0;
  min-height: 100px;
}
</style>
