<template>
    <div class="taohong-operate" style="overflow-y: auto;">
        <a-spin tip="Loading..." :spinning="state.loadLoading">
            <a-tabs class="tabs" v-model:activeKey="state.activeKey" centered>
                <template #leftExtra>
                    <a-button type="link" class="back-btn" @click="$router.back()">
                        <template #icon>
                            <LeftOutlined />
                        </template>
                        返回
                    </a-button>
                    <span>{{ state.query.handle == 'edit' ? '编辑' : '新增' }}模版</span>
                </template>
                <template #rightExtra>
                    <div style="padding-right:15px;">
                        <a-button type="primary" @click="submitForm" :loading="state.btnLoading">发 布</a-button>
                    </div>
                </template>
            </a-tabs>
            <!--  -->
            <div class="body">
                <div class="content" ref="container">
                    <div class="a4" style="padding: 20mm 17mm;">
                        <!--  -->
                        <div v-if="formState.headerConfigDetail.isSecretLevel">密级：【密级】</div>
                        <div v-if="formState.headerConfigDetail.isEmergency" style="margin-top: 10px;">紧急程度：【紧急程度】</div>
                        <div style="text-align: center;position: relative;padding-bottom: 30px;" :style="{fontSize: formState.headerConfigDetail.textSize + 'px',fontFamily: formState.headerConfigDetail.font,
                            color: formState.headerConfigDetail.colour,
                            borderBottom: `${formState.headerConfigDetail.lineWidth}px solid ${formState.headerConfigDetail.colour}`
                        }">
                            {{ formState.headerConfigDetail.headerName }}

                            <span v-if="formState.headerConfigDetail.isDocumentNumber" style="position: absolute;right: 0;bottom: 10px;font-size: 14px;color:#333">文号：【文号】</span>
                        </div>                        
                        <div  v-if="formState.headerConfigDetail.headerHorizontalLine == 'double'" :style="{height: formState.headerConfigDetail.lineWidth + 'px',background: formState.headerConfigDetail.colour,width: '100%',marginTop: '3px'}"></div>
                        <!--  -->
                        <div style="text-align: center;margin-top: 50px;">
                            <div style="font-size: 16px;color: #000;">[正文标题]</div>
                            <div style="margin-top: 10px;">正文内容</div>
                        </div>
                        <div style="position: absolute;bottom: 17mm;left: 20mm;right: 20mm;">
                            <div :style="{height: formState.footerConfigDetail.lineWidth + 'px',background: formState.headerConfigDetail.colour,width: '100%',marginTop: '3px'}"></div>
                            <div  v-if="formState.footerConfigDetail.footerHorizontalLine == 'double'" :style="{height: formState.footerConfigDetail.lineWidth + 'px',background: formState.headerConfigDetail.colour,width: '100%',marginTop: '3px'}"></div>
                        </div>
                        <!--  -->
                        
                    </div>
                </div>
                <div class="setting">
                    <a-form :model="formState" ref="formRef" style="padding:40px 20px;" :label-col="{ style: { width: '80px' } }"  :wrapper-col="{ span: 14 }">
                        <a-form-item label="模板名称" name="templateName"
                            :rules="[{ required: true, message: '请输入模版名称!' }]">
                            <a-input v-model:value="formState.templateName" />
                        </a-form-item>
                        <a-divider orientation="left">版头设置</a-divider>

                        <a-form-item label="版头名称" style="margin-bottom: 16px;">
                            <a-input v-model:value="formState.headerConfigDetail.headerName" />
                        </a-form-item>
                        <a-form-item label="文字大小" style="margin-bottom: 16px;width: 100%;">
                            <a-select ref="select" v-model:value="formState.headerConfigDetail.textSize">
                                <a-select-option :value="24">24px</a-select-option>
                                <a-select-option :value="26">26px</a-select-option>
                                <a-select-option :value="28">28px</a-select-option>
                                <a-select-option :value="30">30px</a-select-option>
                                <a-select-option :value="32">32px</a-select-option>
                                <a-select-option :value="34">34px</a-select-option>
                                <a-select-option :value="36">36px</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="字体" style="margin-bottom: 16px;">
                            <a-select v-model:value="formState.headerConfigDetail.font" style="width: 100%" :options="fontFamilyOptions">
                                <template #option="{ value, label }">
                                    <span :style="{ fontFamily: value, fontWeight: 400 }">
                                        {{ label }}</span>
                                </template>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="颜色"  style="margin-bottom: 16px;">
                            <a-dropdown arrow placement="bottom" :trigger="['click']">
                                <a-button :style="`background-color: ${formState.headerConfigDetail.colour};`"></a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item disabled style="padding: 0;">
                                            <SketchPicker v-model="formState.headerConfigDetail.colour" />
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </a-form-item>
                        <a-form-item label="版头横线" style="margin-bottom: 16px;">
                            <a-space>
                                <a-select v-model:value="formState.headerConfigDetail.headerHorizontalLine" style="width: 120px"
                                    :options="lineOptions">
                                    <template #option="{ value, label }">
                                        <div v-if="value == 'single'"
                                            style="display: flex;align-items: center;height: 100%;">
                                            <div style="border-bottom: 1px solid black; height: 2px;flex: 1;"></div>
                                        </div>
                                        <div v-else style="margin-top: 8px;">
                                            <div style="border-bottom: 1px solid black; height: 2px;"></div>
                                            <div style="border-bottom: 1px solid black; height: 2px;"></div>
                                        </div>
                                    </template>
                                </a-select>
                                <a-select v-model:value="formState.headerConfigDetail.lineWidth" style="width: 100px;">
                                    <a-select-option :value="index + 1" v-for="(item, index) in 8"
                                        :key="item">{{ index + 1 }}</a-select-option>
                                </a-select>
                            </a-space>
                        </a-form-item>
                        <div style="width: 100%;display: flex;justify-content: space-around;margin-bottom: 16px;">
                            <a-checkbox v-model:checked="formState.headerConfigDetail.isSecretLevel">密级</a-checkbox>
                            <a-checkbox v-model:checked="formState.headerConfigDetail.isEmergency">紧急程度</a-checkbox>
                            <a-checkbox v-model:checked="formState.headerConfigDetail.isDocumentNumber">文号</a-checkbox>
                        </div>
                        <a-divider orientation="left">版记设置</a-divider>
                        <a-form-item label="版头横线" >
                            <a-space>
                                <a-select v-model:value="formState.footerConfigDetail.footerHorizontalLine" style="width: 120px"
                                    :options="lineOptions">
                                    <template #option="{ value, label }">
                                        <div v-if="value == 'single'"
                                            style="display: flex;align-items: center;height: 100%;">
                                            <div style="border-bottom: 1px solid black; height: 2px;flex: 1;"></div>
                                        </div>
                                        <div v-else style="margin-top: 8px;">
                                            <div style="border-bottom: 1px solid black; height: 2px;"></div>
                                            <div style="border-bottom: 1px solid black; height: 2px;"></div>
                                        </div>
                                    </template>
                                </a-select>
                                <a-select v-model:value="formState.footerConfigDetail.lineWidth" style="width: 100px;">
                                    <a-select-option :value="index + 1" v-for="(item, index) in 8"
                                        :key="item">{{ index + 1 }}</a-select-option>
                                </a-select>
                            </a-space>
                        </a-form-item>
                    </a-form>
                </div>
            </div>

        </a-spin>
    </div>
</template>

<script setup>

import { onMounted, reactive } from 'vue';
import { LeftOutlined } from '@ant-design/icons-vue'
import http from '@/utils/http'
import { deobfuscate } from '@/utils/common.js';
import { useRoute, useRouter } from 'vue-router';
import { SketchPicker } from 'vue-color'
import 'vue-color/style.css';
import { message } from 'ant-design-vue';

const formRef = ref(null)
const route = useRoute()
const router = useRouter()

const fontFamilyOptions = [
    {
        label: "微软雅黑",
        value: "Microsoft YaHei, Arial, sans-serif",
    },
    {
        label: "宋体",
        value: 'SimSun, Times New Roman, serif',
    },

    {
        label: "楷体",
        value: "KaiTi, 楷体",
    },
    {
        label: "隶书",
        value: "LiSu, 隶书",
    },
    {
        label: "黑体",
        value: "SimHei, 黑体",
    },
];

const lineOptions = [
    {
        label: "单线",
        value: "single",
    },
    {
        label: "双线",
        value: "double",
    },
]

const state = reactive({
    loadLoading: false,
    activeKey: "1",
    btnLoading: false,
    query: {}
})

const formState = reactive({
    templateName: '',
    headerConfigDetail:{
        headerName:"xxxx中心学校",
        textSize:28,
        font:"SimSun, Times New Roman, serif",
        colour: '#ff0000',
        headerHorizontalLine:'single',
        lineWidth:2,
        isSecretLevel:true,
        isEmergency:true,
        isDocumentNumber:true
    },
    footerConfigDetail:{
        footerHorizontalLine:'single',
        lineWidth:2
    },
   
})

function submitForm() {
    formRef.value.validate().then((values) => {
        state.btnLoading = true
        const params = {
            ...formState
        }
        let url = `/cloud/official-doc/red-letterhead/create`
        if(state.query.handle == 'edit') {
            params.id = state.query.id
            url = `/cloud/official-doc/red-letterhead/update`
        }
        http.post(url,params).then((res) => {            
            message.success(res.message)
                setTimeout(() => {
                    router.back()
                }, 1000)
        }).catch((err) => {
            console.error(err)            
        }).finally(() => {
            state.btnLoading = false
        })
    })
}

function init() {
    const query = deobfuscate(route.query.code)
    console.log(query)
    if (query.errorMsg) {
        message.error("参数异常，请重新打开页面")
        setTimeout(() => {
            router.back()
        }, 1500)
    } else {
        state.query = query
        if (query.id) {
            state.loadLoading = true
            http.post("/cloud/official-doc/red-letterhead/detail",{
                id: query.id
            }).then(res => {
                formState.templateName = res.data.templateName
                formState.headerConfigDetail = res.data.headerConfigDetail
                formState.footerConfigDetail = res.data.footerConfigDetail
            }).catch(err => {
                console.error(err)
            }).finally(() => {
                state.loadLoading = false
            })
        }
    }
}

onMounted(() => {
    init()
})
</script>

<style lang="less" scoped>
.taohong-operate {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 600;
    min-height: 100%;
    background-color: #f7f8fa;

    .body {
        margin-top: 48px;
        display: flex;

        .content {
            padding: 24px 350px 24px 24px;
            flex: 1;
            display: flex;
            justify-content: center;
            transform: scale(0.70);
            transform-origin: top center;
        }

        .a4 {
            width: 210mm;
            height: 297mm;
            background-color: #fff;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
            position: relative;
        }
    }

    .setting {
        width: 350px;
        background-color: #fff;
        position: fixed;
        top: 48px;
        right: 0;
        bottom: 0;
        border-left: 1px solid rgba(5, 5, 5, 0.06);

        :deep(.ant-divider-inner-text) {
            font-size: 14px;
            color: #333;
        }
    }

    :deep(.ant-spin-nested-loading) {
        height: 100%;
    }

    .tabs {
        min-height: 100%;

        :deep(.ant-tabs-nav) {
            margin-bottom: 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            height: 48px;
        }

        :deep(.ant-tabs-nav) {
            background-color: #fff;
        }
    }


    .back-btn {
        color: #333;

        &:hover {
            color: #00b781;
        }
    }
}
</style>