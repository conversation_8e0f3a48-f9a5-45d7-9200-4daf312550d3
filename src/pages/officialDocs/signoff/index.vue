
<template>
    <TabsPage type="memoDrafts"></TabsPage>
</template>

<script setup>
import { onMounted,reactive } from 'vue';
import TabsPage from "../components/tabsPage.vue"


    const state = reactive({
        activeKey:'1'
    })

    function init(){

    }

    onMounted(()=>{
        init()
    })
</script>

<style lang="less" scoped>
    .container{
        position: relative;
    }
</style>