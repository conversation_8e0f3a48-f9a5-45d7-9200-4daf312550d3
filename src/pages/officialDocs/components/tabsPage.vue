<template>
    <a-tabs v-model:activeKey="state.activeKey" @change="change">
        <template #leftExtra>
            <div style="width: 16px;"></div>
        </template>
        <a-tab-pane key="2" tab="我收到的">
            <TableList :type="props.type" show-type="2" ref="table2"/>
        </a-tab-pane>
        <a-tab-pane key="1" tab="我发布的" force-render>
            <TableList  :type="props.type" show-type="1" ref="table1"/>
        </a-tab-pane>
    </a-tabs>
</template>

<script setup>

import { onMounted, reactive,ref } from 'vue';
import TableList from "./tableList.vue"
const table1 = ref()
const table2 = ref()
const props = defineProps({
    type: {
        type: String,
        default: ''
    }
})

const state = reactive({
    activeKey: '2',
})
function change(e) {
    console.log(e)
    if(e==2){
        table2.value?.init()
    }
    if(e==1){
        table1.value?.init()
    }
}

</script>

<style lang="less" scoped>
.container {
    position: relative;
}
</style>