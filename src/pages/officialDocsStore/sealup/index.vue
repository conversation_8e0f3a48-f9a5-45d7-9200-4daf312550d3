
<template>
    <a-tabs v-model:activeKey="state.activeKey" @chang="change">
        <template #leftExtra>
            <div style="width: 16px;"></div>
        </template>
        <a-tab-pane key="1" tab="发文归档">
            <Dispatch type="docDrafts" isSealup v-if="state.activeKey==1"/>
        </a-tab-pane>
        <a-tab-pane key="2" tab="收文归档">
            <Receiving type="docReceipts" isSealup v-if="state.activeKey==2"/>
        </a-tab-pane>
        <a-tab-pane key="3" tab="签报归档">
            <Signoff type="memoDrafts" isSealup v-if="state.activeKey==3"/>
        </a-tab-pane>
    </a-tabs>
</template>

<script setup>

    import { onMounted,reactive } from 'vue';

    import Dispatch from '../dispatch/index.vue';
    import Receiving from '../receiving/index.vue';
    import Signoff from '../signoff/index.vue';

    const state = reactive({
        activeKey: '1',
    })

    function change(e){
        console.log(e)
    }    
</script>

<style lang="less" scoped>
    
</style>