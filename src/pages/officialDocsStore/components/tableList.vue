<template>
    <div class="container-page">
        <!-- search -->
        <a-form layout="inline" :model="search" ref="searchRef">
            <a-form-item :label="`${type == 'signoff' ? '签报' : '文件'}标题`" name="title" style="margin-bottom: 16px">
                <a-input v-model:value="search.title" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item :label="`${authorName}：`" name="authorName" style="margin-bottom: 16px;">
                <a-input v-model:value="search.authorName" placeholder="请输入" style="width: 120px;"></a-input>
            </a-form-item>
            <a-form-item label="机密程度：" name="securityClassification" style="margin-bottom: 16px">
                <a-select v-model:value="search.securityClassification" style="width: 120px;"
                    :options="[{ value: '', label: '全部' }, ...store.securityOptions]">
                </a-select>
            </a-form-item>
            <a-form-item label="紧急程度：" name="urgencyLevel" style="margin-bottom: 16px">
                <a-select v-model:value="search.urgencyLevel" style="width: 120px;"
                    :options="[{ value: '', label: '全部' }, ...store.urgencyLevelOptions]">
                </a-select>
            </a-form-item>
            <a-form-item label="文件类型：" name="fileType" style="margin-bottom: 16px">
                <a-select v-if="type == 'memoDrafts'" v-model:value="search.fileType" style="width: 120px;"
                    :options="[{ value: '', label: '全部' }, ...store.signoffOptions]"></a-select>
                <a-select v-else v-model:value="search.fileType" style="width: 120px;"
                    :options="[{ value: '', label: '全部' }, ...store.fileTypeOptions]"></a-select>
            </a-form-item>
            <template v-if="!isSealup">
                <a-form-item label="当前状态：" name="status" style="margin-bottom: 16px">
                    <a-select v-model:value="search.status" style="width: 120px;" :options="store.statusOptions">
                    </a-select>
                </a-form-item>
            </template>
            <a-form-item label="创建时间：" name="picker" style="margin-bottom: 16px">
                <a-range-picker v-model:value="search.picker" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item style="margin-bottom: 16px">
                <a-button type="primary" @click="submitSearch">
                    <template #icon>
                        <SearchOutlined />
                    </template>查询
                </a-button>
                <a-button class="delBtn" @click="resetSearch">
                    <template #icon>
                        <RedoOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-form>
        <!-- table -->
        <a-table rowKey="id" size="small" :dataSource="state.list" :columns="columns" bordered class="table"
            :pagination="pagination" @change="changePagination" :loading="state.loading">
            <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'operation'">
                    <a-button type="link" @click="handleOpenModal('view', record)">查看</a-button>
                </template>
            </template>
        </a-table>
    </div>
</template>

<script setup>
import { onMounted, ref, createVNode,computed } from 'vue';
import { SearchOutlined, RedoOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue';
import { useRouter, useRoute } from "vue-router";
import { message, Modal } from "ant-design-vue"
import { debounce, obfuscate } from "@/utils/common";

const router = useRouter();
const route = useRoute();

const store = useStore();

const props = defineProps({
    columns: {
        type: Array,
        default: () => []
    },
    type: {
        type: String,
        default: ''
    },
    isSealup: {
        type: Boolean,
        default: false
    }
})

const authorName = computed(()=>{
  const labelMap = {
    'docDrafts':'拟稿人',
    'docReceipts':'登记人',
    'memoDrafts':'拟稿人'
  }
  return labelMap[props.type]
})

const API = {
    // create: (p) => http.post('/manage/scoring/template/group/create', p),
    // delete: (p) => http.post('/manage/scoring/template/group/delete', p),
    query: (p) => http.post('/cloud/official-doc/document/page', p),
    // update: (p) => http.post('/manage/scoring/template/group/update', p),
}

// https://www.antdv.com/components/form-cn#components-form-demo-basic
// https://www.antdv.com/components/table-cn


const searchRef = ref();

const pagination = reactive({
    total: 0,
    size: '10',
    showQuickJumper: true,
    showLessItems: true,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '30', '40', '100'],
    showTotal: (total, range) => `共 ${total} 条`,
});

const search = reactive({
    status: '',
    securityClassification: '',
    urgencyLevel: '',
    fileType: ''
});

const state = reactive({
    list: [],
    pageSize: 10,
    pageNo: 1,
    loading: false,
    isSearch: false,
});


const submitSearch = debounce(() => {
    state.isSearch = true
    init();
}, 500)

const resetSearch = debounce(() => {
    searchRef.value.resetFields();
    state.pageNo = 1;
    state.isSearch = false
    init();
}, 500)

function handleOpenModal(type, data) {
    // console.log(type, data)
    router.push({
        name: "draftOperate",
        query: {
            code: obfuscate(JSON.stringify({
                type: data.type,
                source: route.name,
                id: data.id,
                handle: type,
            })),
        },
    })
}

function submitDelete(ids, cb) {
    API.delete({
        ids
    }).then(res => {
        message.success(res?.data?.message || res?.message || '操作成功');
        cb && cb()
    }).catch(err => {
        console.log(err)
    })
}

function changePagination({ current, pageSize }) {
    state.pageSize = pageSize;
    state.pageNo = current;
    init()
}

async function init() {
    try {
        state.loading = true;
        let params = {
            pageSize: state.pageSize,
            pageNo: state.pageNo,
            type: props.type,
        };
        if (state.isSearch) {
            params = {
                ...params,
                ...search,
            };
            if (search.picker?.length > 0) {
                params.startDate = search.picker[0]
                params.endDate = search.picker[1]
            }
        }
        if (props.isSealup) {
            params.status = 'YI_GUI_DANG'
        }
        const { data } = await API.query(params);
        const { list, pageNo, total } = data || {};
        state.pageNo = +pageNo || 1;
        pagination.total = +total || 0;
        state.list = list || [];

        // 如果当前只有一条删除后无数据返回上一页
        if (+pageNo > 1 && list.length == 0) {
            state.pageNo--
            init()
        }
    } catch (error) {
        console.log(error);
    } finally {
        nextTick(() => {
            state.loading = false;
        });
    }
}

onMounted(() => {
    init();
});
</script>

<style lang="less" scoped>
.container-page {
    position: relative;
    padding: 16px;

    .handle {
        display: flex;
        align-items: center;
        justify-content: end;
    }

    .table {
        padding-top: 16px;
    }
}
</style>
