<template>
    <TableList :columns="columns" type="docReceipts" :isSealup="isSealup" />
</template>

<script setup>
import { onMounted, reactive } from 'vue';
import TableList from '../components/tableList.vue';
const props = defineProps({
    isSealup: {
        type: Boolean,
        default: false
    }
})

const defaultColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        align: 'center',
        customRender: row => `${row.index + 1}`,
        width: 70,
    },
    {
        title: '文件标题',
        dataIndex: 'title',
        ellipsis: true,
         width: 320,
    },
    {
        title: '来文字号',
        dataIndex: 'docNo',
        ellipsis: true,
    },
    {
        title: '文件类型',
        dataIndex: 'fileTypeCode',
        ellipsis: true,
    },
    {
        title: '登记人',
        dataIndex: 'authorName',
        ellipsis: true,
    },
    {
        title: '机密程度',
        dataIndex: 'securityClassificationCode',
        ellipsis: true,
    },
    {
        title: '紧急程度',
        dataIndex: 'urgencyLevelCode',
        ellipsis: true,
    },
    {
        title: '当前状态',
        dataIndex: 'statusCode',
        ellipsis: true,
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        ellipsis: true,
        width: 180,
    },

    {
        title: '操作',
        dataIndex: 'operation',
        align: 'left',
        fixed: 'right',
    },
];

const columns = computed(() => {
    return props.isSealup ? defaultColumns.filter(item => !['statusCode'].includes(item.dataIndex)) : defaultColumns
})
const state = reactive({

})

function init() {

}

onMounted(() => {
    init()
})
</script>

<style lang="less" scoped></style>