<template>
  <div class="container-page">
    <!-- search -->
    <a-form layout="inline" :model="search" ref="searchRef">
      <a-form-item label="模板名称" name="formName" style="margin-bottom: 16px">
        <a-input
          v-model:value="search.formName"
          placeholder="请输入"
          style="width: 120px"
        ></a-input>
      </a-form-item>
      <a-form-item label="模板状态" name="isStop" style="margin-bottom: 16px">
        <a-select
          v-model:value="search.isStop"
          style="width: 120px"
          :options="[
            { value: '', label: '全部' },
            ...store.templateStatusOptions,
          ]"
        >
        </a-select>
      </a-form-item>
      <a-form-item style="margin-bottom: 16px">
        <a-button type="primary" @click="submitSearch">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button class="delBtn" @click="resetSearch">
          <template #icon>
            <RedoOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-form>
    <!--  -->
    <div class="handle">
      <a-button type="primary" @click="handleOpenModal('add')"
        >新增模板</a-button
      >
    </div>

    <!-- table -->
    <a-table
      rowKey="id"
      size="small"
      :dataSource="state.list"
      :columns="columns"
      bordered
      class="table"
      :pagination="pagination"
      @change="changePagination"
      :loading="state.loading"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'operation'">
          <a-button type="link" style="padding: 0 8px;" @click="handleOpenModal('edit', record)"
            >编辑</a-button
          >
          <a-button v-if="!record.isDefault" type="link" style="margin-left: 0;padding: 0 8px;" @click="enableTemp(record)">{{ record.isStop ? '启用' :  '禁用' }}</a-button>
          <a-popconfirm
            v-if="state.list.length"
            title="确认删除？"
            @confirm="removeTemp(record)"
          >            
            <a-button v-if="!record.isDefault" type="link" style="margin-left: 0;padding: 0 8px;">删除</a-button>
          </a-popconfirm>
          <!-- <a-button type="link" @click="handleOpenModal('view', record)"
            >查看</a-button
          > -->
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { onMounted, ref, createVNode, computed } from "vue";
import {
  SearchOutlined,
  RedoOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons-vue";
import { useRouter, useRoute } from "vue-router";
import { message, Modal } from "ant-design-vue";
import { debounce, obfuscate } from "@/utils/common";

const router = useRouter();
const route = useRoute();

const store = useStore();

const { templateStatusOptions } = storeToRefs(store);

const props = defineProps({
  columns: {
    type: Array,
    default: () => [
      {
        title: "序号",
        dataIndex: "index",
        align: "center",
        customRender: (row) => `${row.index + 1}`,
        width: 70,
      },
      {
        title: "模版名称",
        dataIndex: "formName",
        ellipsis: true,
        width: 320,
      },
      {
        title: "当前状态",
        dataIndex: "isStop",
        ellipsis: true,
        customRender: ({record}) => {
            if(record.isStop){
                return '禁用'
            }
            return '启用'
        },
      },
      {
        title: "创建时间",
        dataIndex: "createTime",
        ellipsis: true,
        width: 180,
      },

      {
        title: "操作",
        dataIndex: "operation",
        align: "center",
        fixed: "right",
      },
    ],
  },
  type: {
    type: String,
    default: "",
  },
});

const API = {
  // create: (p) => http.post('/manage/scoring/template/group/create', p),
  delete: (p) => http.get("/cloud/official-doc/process-template/delete", p),
  query: (p) => http.post("/cloud/official-doc/process-template/page", p),
  // update: (p) => http.post('/manage/scoring/template/group/update', p),
  enable: (p) => http.post("/cloud/official-doc/process-template/updateProcessStop", p),
};

// https://www.antdv.com/components/form-cn#components-form-demo-basic
// https://www.antdv.com/components/table-cn

const searchRef = ref();

const pagination = reactive({
  total: 0,
  size: "10",
  showQuickJumper: true,
  showLessItems: true,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "30", "40", "100"],
  showTotal: (total, range) => `共 ${total} 条`,
});

const search = reactive({
  formName: "",
  isStop: "",
});

const state = reactive({
  list: [],
  pageSize: 10,
  pageNo: 1,
  loading: false,
  isSearch: false,
});

const submitSearch = debounce(() => {
  state.isSearch = true;
  init();
}, 500);

const resetSearch = debounce(() => {
  searchRef.value.resetFields();
  state.pageNo = 1;
  state.isSearch = false;
  init();
}, 500);

function handleOpenModal(type, data) {
  // console.log(type, data)
  router.push({
    path: "/template/operate",
    query: {
      code: obfuscate(
        JSON.stringify({
          type: props.type,
          source: route.name,
          id: data?.id,
          handle: type,
        })
      ),
    },
  });
}

async function enableTemp({ id, isStop }) {
    await API.enable({
        id,
        isStop: !isStop,
    }).then(res=>{
        message.success(res?.data?.message || res?.message || "操作成功");
        init();
    })
}

function removeTemp({ id }) {
  API.delete({
    id,
  })
    .then((res) => {
      message.success(res?.data?.message || res?.message || "操作成功");
      init();
    })
    .catch((err) => {
      console.log(err);
    });
}

function changePagination({ current, pageSize }) {
  state.pageSize = pageSize;
  state.pageNo = current;
  init();
}

async function init() {
  try {
    state.loading = true;
    let params = {
      pageSize: state.pageSize,
      pageNo: state.pageNo,
      type: props.type,
    };
    if (state.isSearch) {
      params = {
        ...params,
        ...search,
      };
      if (search.picker?.length > 0) {
        params.startDate = search.picker[0];
        params.endDate = search.picker[1];
      }
    }   
    const { data } = await API.query(params);
    const { list, pageNo, total } = data || {};
    state.pageNo = +pageNo || 1;
    pagination.total = +total || 0;
    state.list = list || [];

    // 如果当前只有一条删除后无数据返回上一页
    if (+pageNo > 1 && list.length == 0) {
      state.pageNo--;
      init();
    }
  } catch (error) {
    console.log(error);
  } finally {
    nextTick(() => {
      state.loading = false;
    });
  }
}

onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.container-page {
  position: relative;
  padding: 16px;

  .handle {
    display: flex;
    align-items: center;
    justify-content: end;
  }

  .table {
    padding-top: 16px;
  }
}
</style>
