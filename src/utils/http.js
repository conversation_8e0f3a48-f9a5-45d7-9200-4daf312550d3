import { message } from 'ant-design-vue'
import axios from 'axios'
import { useRouter } from 'vue-router'
const store = useStore()
const router = useRouter()
axios.defaults.baseURL = import.meta.env.VITE_BASE_API

/** 参数是否一致 */
let postStr = {}
const isRepeat = (url, data) => {
    let flag = true
    const key = url + JSON.stringify(data)
    if (postStr[key]) {
        flag = false
    } else {
        flag = true
        postStr[key] = true
    }
    return flag
}

const getToken = () => {
    if (store.token) {
        return store.token.includes('Bearer') ? store.token : `Bearer ${store.token}`
    } else {
        window.location.replace('/#/login')
    }
}
// 请求拦截器
axios.interceptors.request.use(
    config => {
        config.headers.Authorization = getToken()
        config.headers['platform'] = 'officialDocs'
        return config
    },
    error => {
        return Promise.reject(error)
    },
)

const toLoginCode = [
    1001001001, 1001001002, 1001001003, 1001001005, 1001001006, 1001001007, 1002002002, 1002002009, 1002002012, 1002002013, 401, 1004, 1003,
]
// 响应拦截器
axios.interceptors.response.use(
    response => {
        const { data, config, status } = response        
        if (config.method == 'post') {
            // 计算当前URL传递的参数
            const key = config.url + config.data
            if (postStr[key]) {
                postStr[key] = null
            }
        }
        
        if (config?.responseType == 'arraybuffer') {
            return data
        }
        if (status == 200 && data.code == 0) {
            return data
        } else if (status == 200 && data.code == 1002002014) {
            message.error(data?.message)
            if (window.location.href.indexOf('/#/login')) {
                // 本身在登录页(不跳转到没有权限页面)
                return data
            } else {
                router.replace({
                    path: '/no-auth',
                })
            }
        } else if (toLoginCode.includes(data.code)) {
            store.clearUser()
            window.localStorage.clear()
            message.error(data.message)
            router.push({
                path: '/login',
            })
            // setTimeout(() => {
            //     window?.close()
            // }, 2000)            

            return data
        } else {
            message.error(data?.message || '未知异常')
            return Promise.reject(data)
        }
    },
    error => {
        return Promise.reject(error)
    },
)
const get = (url, params = {}) => {
    return axios({
        method: 'get',
        url: url,
        params,
    })
}
const post = (url, data, params, headers = { 'Content-Type': 'application/json' }) => {
    const flag = isRepeat(url, data)
    if (flag) {
        return axios({
            method: 'post',
            url: url,
            headers,
            data,
            params,
        })
    } else {
        return Promise.reject()
    }
}

const download = (url, data, name, fn, type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') => {
    return axios({
        url,
        method: 'post',
        data,
        responseType: 'arraybuffer',
    }).then(blob => {
        try {
            // $ 当下载excel文件正常时content-type类型application/vnd.ms-excel;charset=utf-8 不正常时才是application/json
            // ArrayBuffer需要将其转换为字符串来检查内容
            const data = new TextDecoder('utf-8').decode(blob)
            // 如果类型为application/json表示不是excel文件，解析成功，否则报错
            const jsonData = JSON.parse(data)
            YMessage.error(jsonData.message)
            return false
        } catch (error) {
            const url = window.URL.createObjectURL(
                new Blob([blob], {
                    type,
                }),
            )
            const link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', `${name}`)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            fn && fn()
            setTimeout(() => window.URL.revokeObjectURL(url), 1000)
            return true
        }
    })
}
const form = (url, data) => {
    let fd = new FormData()
    for (let key in data) {
        fd.append(key, data[key])
    }
    return axios({
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        url: url,
        data: fd,
    })
}

const put = (url, data) => {
    return axios({
        method: 'put',
        url: url,
        data,
    })
}

export default { get, post, download, form, put }
export { axios }
