<template>
    <div>
        <a-config-provider :locale="zh_CN" :theme="{
            token: {
                colorPrimary: `#00B781`,
            },
        }">
            <router-view></router-view>
        </a-config-provider>
    </div>
</template>
<script setup>
import { onMounted } from "vue"
import { useRoute } from "vue-router"
import zh_CN from 'ant-design-vue/es/locale/zh_CN'
import 'dayjs/locale/zh-cn'
import config from "@/config"

const route = useRoute()

onMounted(() => {
    window.document.title = config.title       
    // console.log('route', route)    
})



</script>
<style></style>
