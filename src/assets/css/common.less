* {
    padding: 0;
    margin: 0;
    -webkit-print-color-adjust: exact !important;
}
:root {
    --primary-color: @primary-color;
    --link-color: @link-color;
    --success-color: @success-color; // 成功色
    --warning-color: @warning-color; // 警告色
    --error-color: @error-color; // 错误色
    --font-size-base: @font-size-base; // 主字号
    --heading-color: @heading-color; // 标题色
    --text-color: @text-color; // 主文本色，
    --suggestive-color: @suggestive-color; // 提示性文字
    --text-color-secondary: @text-color-secondary; // 次文本色
    --disabled-color: @disabled-color; // 失效色
    --border-radius-base: @border-radius-base; // 组件/浮层圆角
    --border-color-base: @border-color-base; // 边框色
    --box-shadow-base: @box-shadow-base; // 浮层阴影
    --body-background: @body-background; //白色
    --gray-background: @gray-background; //灰色背景
    --acitve-background: @acitve-background; //选中颜色
}

li {
    list-style: none;
}

.icon {
    fill: currentColor;
    overflow: hidden;
}

.scrollbar {
    overflow: auto;
    position: relative;
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}

.ellipsis2 {
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.ant-btn + .ant-btn {
    margin-left: 12px;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 2px;
    box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
}

@media print {
    @page {
        margin: 0;
    }
}

.table_backgroundcolor_worning {
    background-color: #fff6ea;
    .ant-table-cell-row-hover {
        background: #fff6ea !important;
    }
}

.throttle {
    animation: throttle 1.5s step-end forwards;
}
.throttle:active {
    animation: none;
}
@keyframes throttle {
    from {
        pointer-events: none;
    }

    to {
        pointer-events: all;
    }
}

// 隐藏Edge浏览器密码显示图标
input::-ms-reveal,
input::-ms-clear {
    display: none;
}
