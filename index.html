<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="icon" type="image/svg+xml" href="/logo.png" />
        <link rel="stylesheet" href="//at.alicdn.com/t/c/font_4545192_0at44z60wqv.css" />
        <script src="//at.alicdn.com/t/c/font_4545192_0at44z60wqv.js"></script>
        <link rel="stylesheet" href="https://file.1d1j.cn/siderIcon/iconfont.css"/>
        <script src="https://file.1d1j.cn/siderIcon/iconfont.js"></script>
        <title></title>
    </head>
    <body>
        <div id="app">
            <div id="loading-mask">
                <style>
                    #loading-mask {
                        position: fixed;
                        left: 50%;
                        top: 30%;
                        display: flex;
                        flex-direction: column;
                    }

                    .spinner {
                        overflow: hidden;
                        width: 35px;
                        height: 60px;
                        background: #fff;
                        user-select: none;
                        z-index: 9999;
                        transform: translate3d(-50%, -50%);
                    }

                    .loading-text {
                        position: relative;
                        top: 40px;
                    }

                    .double-bounce1,
                    .double-bounce2 {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        background-color: #00b781;
                        opacity: 0.6;
                        position: absolute;
                        top: 0;
                        left: 0;
                        animation: bounce 2s infinite ease-in-out;
                    }

                    .double-bounce2 {
                        animation-delay: -1s;
                    }

                    @keyframes bounce {
                        0%,
                        100% {
                            transform: scale(0);
                        }

                        50% {
                            transform: scale(0.8);
                        }
                    }
                </style>
                <div class="spinner">
                    <div class="double-bounce1"></div>
                    <div class="double-bounce2"></div>
                </div>
                <span class="loading-text">正在加载...</span>
            </div>
        </div>
        <script type="text/javascript" src="/js/base64.js"></script>
        <script type="module" src="/src/main.js"></script>
    </body>
</html>
