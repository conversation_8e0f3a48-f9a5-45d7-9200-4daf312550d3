# 公文管理

公文管理 云平台应用。

## 安装
```bash
  pnpm install
```

## 运行
```bash
  npm run dev
```

## 部署/构建
```bash
  npm run build:uat  # 测试环境
  npm run build:uatrelease  # 预发布环境
  npm run build:main  # 生产环境
```
|  环境 | 帐号     | 密码                |
| :-------- | :------- | :------------------------- |
| 测试环境 | - | - | http://*************:8043
| 预发布环境 | - | - | https://docsuat.yyide.vip
| 生产环境 | - | - | https://docs.yyide.com

`本地部署起始分支`：`base-local`

> 注意：
> 1. 本地部署基于 `base-local` 分支进行开发，`base-local` 分支为基础分支，不允许直接在 `base-local` 分支上开发，必须新建分支进行开发，之后合并进来。
> 2. 后续不同学校部署定制命名采用 `local-xx学校`


## 相关文档
- [代码地址](https://git.yyide.com/cloud/official-docs-pc)

- [API 文档](https://app.apifox.com/project/2934512/apis/api-306463258)

- [Jenkins](http://*************:8000/view/uat-web/job/uat-web-official-docs-pc/)

- [原型 地址](https://lanhuapp.com/web/#/item/project/product?tid=3aae0c0e-5180-4f02-817b-fc646005e0a7&pid=465bba9a-a616-4a3c-8483-d4dfdce578f2&versionId=1b53dced-7d3b-4b44-adad-757826abe404&docId=8e445135-64c6-467c-a593-ac11461e490d&docType=axure&pageId=29327b1e65d041c68f9e6e61f42f462b&image_id=8e445135-64c6-467c-a593-ac11461e490d&parentId=e1edb54a-d31c-4cb8-80c8-d98abb118bb8)

- [设计稿 地址](无)

- [Tapd 需求](https://www.tapd.cn/tapd_fe/32711522/story/detail/1132711522001014927?from_iteration_id=1132711522001000312)
  -  [任务]()

## 技术栈

####  1.业务流程描述

发起 => 审批流 => 结束

####  3.技术选型

vue3 + pinia +  ant-vue + axios 

####  2.页面结构

``` md
src/
├── ...
├── pages/
│   ├── draft/
│   │   ├── components/...          
│   │   ├── dispatch/         # 发文拟稿
│   │   ├── receiving/        # 收入登记
│   │   └── signoff/          # 签报拟稿
│   ├── draftOperate/         # 发文（创建，查看，办理）
│   │   ├── template/...           稿纸模版
│   │   ├── formInfo.vue           稿纸信息
│   │   ├── index.vue              
│   │   ├── log.vue                日志
│   │   └── mainBody.vue           正文
│   ├── home/                # home
│   │   └── index.vue
│   ├── officialDocs/        # 公文管理
│   │   ├── components/...
│   │   ├── dispatch/              发文管理
│   │   ├── receiving/             收文管理
│   │   └── signoff/               签报管理
│   ├── draftOperate/        # 公文库
│   │   ├── components/...
│   │   ├── dispatch/              发文库
│   │   ├── receiving/             收文库
│   │   ├── sealup/                归档库
│   │   └── signoff/               签报库
└── README.md
```

## 注意

- 有些既是页面使用也是被其他页面引入的组件使用，如更改注意区分不同。

## TODO

[ ] 稿纸模版目前是写死的，后续需改成动态渲染。










