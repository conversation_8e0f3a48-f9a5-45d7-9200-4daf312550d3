/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/js-base64@3.6.0/base64.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):function(){const r=e.Base64,o=t();o.noConflict=()=>(e.Base64=r,o),e.Meteor&&(Base64=o),e.Base64=o}()}("undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:this,(function(){"use strict";const e="3.6.0",t=e,r="function"==typeof atob,o="function"==typeof btoa,n="function"==typeof Buffer,a="function"==typeof TextDecoder?new TextDecoder:void 0,f="function"==typeof TextEncoder?new TextEncoder:void 0,i=[..."ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="],c=(e=>{let t={};return e.forEach(((e,r)=>t[e]=r)),t})(i),u=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,s=String.fromCharCode.bind(String),d="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):(e,t=(e=>e))=>new Uint8Array(Array.prototype.slice.call(e,0).map(t)),l=e=>e.replace(/[+\/]/g,(e=>"+"==e?"-":"_")).replace(/=+$/m,""),h=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),p=e=>{let t,r,o,n,a="";const f=e.length%3;for(let f=0;f<e.length;){if((r=e.charCodeAt(f++))>255||(o=e.charCodeAt(f++))>255||(n=e.charCodeAt(f++))>255)throw new TypeError("invalid character found");t=r<<16|o<<8|n,a+=i[t>>18&63]+i[t>>12&63]+i[t>>6&63]+i[63&t]}return f?a.slice(0,f-3)+"===".substring(f):a},y=o?e=>btoa(e):n?e=>Buffer.from(e,"binary").toString("base64"):p,A=n?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let r=0,o=e.length;r<o;r+=4096)t.push(s.apply(null,e.subarray(r,r+4096)));return y(t.join(""))},b=(e,t=!1)=>t?l(A(e)):A(e),g=e=>{if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?s(192|t>>>6)+s(128|63&t):s(224|t>>>12&15)+s(128|t>>>6&63)+s(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return s(240|t>>>18&7)+s(128|t>>>12&63)+s(128|t>>>6&63)+s(128|63&t)},B=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,x=e=>e.replace(B,g),C=n?e=>Buffer.from(e,"utf8").toString("base64"):f?e=>A(f.encode(e)):e=>y(x(e)),m=(e,t=!1)=>t?l(C(e)):C(e),U=e=>m(e,!0),F=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,w=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return s(55296+(t>>>10))+s(56320+(1023&t));case 3:return s((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return s((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},S=e=>e.replace(F,w),E=e=>{if(e=e.replace(/\s+/g,""),!u.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,r,o,n="";for(let a=0;a<e.length;)t=c[e.charAt(a++)]<<18|c[e.charAt(a++)]<<12|(r=c[e.charAt(a++)])<<6|(o=c[e.charAt(a++)]),n+=64===r?s(t>>16&255):64===o?s(t>>16&255,t>>8&255):s(t>>16&255,t>>8&255,255&t);return n},v=r?e=>atob(h(e)):n?e=>Buffer.from(e,"base64").toString("binary"):E,D=n?e=>d(Buffer.from(e,"base64")):e=>d(v(e),(e=>e.charCodeAt(0))),R=e=>D(T(e)),z=n?e=>Buffer.from(e,"base64").toString("utf8"):a?e=>a.decode(D(e)):e=>S(v(e)),T=e=>h(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),Z=e=>z(T(e)),j=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),I=function(){const e=(e,t)=>Object.defineProperty(String.prototype,e,j(t));e("fromBase64",(function(){return Z(this)})),e("toBase64",(function(e){return m(this,e)})),e("toBase64URI",(function(){return m(this,!0)})),e("toBase64URL",(function(){return m(this,!0)})),e("toUint8Array",(function(){return R(this)}))},O=function(){const e=(e,t)=>Object.defineProperty(Uint8Array.prototype,e,j(t));e("toBase64",(function(e){return b(this,e)})),e("toBase64URI",(function(){return b(this,!0)})),e("toBase64URL",(function(){return b(this,!0)}))},P={version:e,VERSION:t,atob:v,atobPolyfill:E,btoa:y,btoaPolyfill:p,fromBase64:Z,toBase64:m,encode:m,encodeURI:U,encodeURL:U,utob:x,btou:S,decode:Z,isValid:e=>{if("string"!=typeof e)return!1;const t=e.replace(/\s+/g,"").replace(/=+$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:b,toUint8Array:R,extendString:I,extendUint8Array:O,extendBuiltins:()=>{I(),O()},Base64:{}};return Object.keys(P).forEach((e=>P.Base64[e]=P[e])),P}));
//# sourceMappingURL=/sm/4b3ca16241a2283fc7ccf819ba910682f51384f2d10693eed5436fc2921d61ff.map